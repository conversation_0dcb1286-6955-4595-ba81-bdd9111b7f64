from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from authentication.models import User


class AgentLevel(models.Model):
    """
    代理商等级配置
    """
    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='等级名称',
        help_text='代理商等级名称，如：金牌代理'
    )

    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='分成比例',
        help_text='分成比例，单位为百分比'
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='是否启用该代理等级'
    )

    fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='代理费',
        help_text='成为该级别代理需要支付的费用'
    )

    service_content = models.TextField(
        blank=True,
        verbose_name='服务内容',
        help_text='该级别代理享受的服务内容，每行一项'
    )

    sort_order = models.IntegerField(
        default=0,
        verbose_name='排序',
        help_text='数字越小越靠前'
    )

    description = models.TextField(
        blank=True,
        verbose_name='描述',
        help_text='代理等级描述'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )

    class Meta:
        db_table = 'agent_level'
        verbose_name = '代理商等级'
        verbose_name_plural = '代理商等级'
        ordering = ['sort_order', 'id']

    def __str__(self):
        return f'{self.name} ({self.commission_rate}%)'


class UserAgent(models.Model):
    """
    用户代理关系
    """
    AGENT_MODE_CHOICES = [
        ('commission', '分成模式'),
        ('prepaid', '预付费模式'),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='agent_profile',
        verbose_name='用户'
    )

    agent_level = models.ForeignKey(
        AgentLevel,
        on_delete=models.PROTECT,
        related_name='user_agents',
        verbose_name='代理等级'
    )

    agent_mode = models.CharField(
        max_length=20,
        choices=AGENT_MODE_CHOICES,
        default='commission',
        verbose_name='代理模式',
        help_text='代理商的运营模式'
    )

    custom_commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        null=True,
        blank=True,
        verbose_name='自定义分成比例',
        help_text='如果设置，将覆盖等级默认分成比例'
    )

    total_credit = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='总额度',
        help_text='预付费模式下的总额度'
    )

    remaining_credit = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='剩余额度',
        help_text='预付费模式下的剩余额度'
    )

    domain = models.CharField(
        max_length=255,
        unique=True,
        null=True,
        blank=True,
        verbose_name='绑定域名',
        help_text='代理商专属域名'
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='是否启用该代理关系'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )

    class Meta:
        db_table = 'user_agent'
        verbose_name = '用户代理关系'
        verbose_name_plural = '用户代理关系'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.user.email} - {self.agent_level.name}'

    @property
    def effective_commission_rate(self):
        """获取有效的分成比例"""
        return self.custom_commission_rate or self.agent_level.commission_rate


class CommissionRecord(models.Model):
    """
    分成记录
    """
    STATUS_CHOICES = [
        ('pending', '待结算'),
        ('settled', '已结算'),
        ('cancelled', '已取消'),
    ]

    agent_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='agent_commissions',
        verbose_name='代理商用户'
    )

    consumer_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='consumer_commissions',
        verbose_name='消费用户'
    )

    order_id = models.CharField(
        max_length=100,
        verbose_name='订单ID',
        help_text='关联的订单ID'
    )

    order_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='订单金额'
    )

    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name='分成比例',
        help_text='实际使用的分成比例'
    )

    commission_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='分成金额'
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='状态'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    settled_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='结算时间'
    )

    class Meta:
        db_table = 'commission_record'
        verbose_name = '分成记录'
        verbose_name_plural = '分成记录'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['agent_user', 'status']),
            models.Index(fields=['consumer_user']),
            models.Index(fields=['order_id']),
        ]

    def __str__(self):
        return f'{self.agent_user.email} - {self.order_id} - ¥{self.commission_amount}'


class RechargeRecord(models.Model):
    """
    充值记录
    """
    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('paid', '已支付'),
        ('cancelled', '已取消'),
    ]

    agent_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='recharge_records',
        verbose_name='代理商用户'
    )

    recharge_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='充值金额'
    )

    credit_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name='获得额度',
        help_text='根据分成比例计算得到的额度'
    )

    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name='分成比例',
        help_text='充值时使用的分成比例'
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='支付状态'
    )

    payment_code = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='支付码',
        help_text='支付二维码标识'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    paid_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='支付时间'
    )

    class Meta:
        db_table = 'recharge_record'
        verbose_name = '充值记录'
        verbose_name_plural = '充值记录'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['agent_user', 'status']),
            models.Index(fields=['payment_code']),
        ]

    def __str__(self):
        return f'{self.agent_user.email} - 充值¥{self.recharge_amount} - {self.get_status_display()}'


class BillRecord(models.Model):
    """
    账单记录（预付费模式）
    """
    agent_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='bill_records',
        verbose_name='代理商用户'
    )

    consumer_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='consumer_bills',
        verbose_name='消费用户'
    )

    order_id = models.CharField(
        max_length=100,
        verbose_name='订单ID',
        help_text='关联的订单ID'
    )

    order_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='订单金额'
    )

    headquarters_commission = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='总部分润',
        help_text='总部分润金额（订单金额 × 总部分润比例）'
    )

    remaining_credit = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name='剩余额度',
        help_text='扣除后的剩余额度'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    class Meta:
        db_table = 'bill_record'
        verbose_name = '账单记录'
        verbose_name_plural = '账单记录'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['agent_user']),
            models.Index(fields=['consumer_user']),
            models.Index(fields=['order_id']),
        ]

    def __str__(self):
        return f'{self.agent_user.email} - {self.order_id} - ¥{self.order_amount}'


class AgentPaymentInfo(models.Model):
    """
    代理商收款信息
    """
    agent_user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='payment_info',
        verbose_name='代理商用户'
    )

    # 银行卡信息
    bank_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='银行名称',
        help_text='开户银行名称'
    )

    bank_account = models.CharField(
        max_length=50,
        blank=True,
        verbose_name='银行卡号',
        help_text='银行卡账号'
    )

    account_holder = models.CharField(
        max_length=50,
        blank=True,
        verbose_name='开户人姓名',
        help_text='银行卡开户人姓名'
    )

    # 微信收款码
    wechat_qr_code = models.TextField(
        blank=True,
        verbose_name='微信收款码',
        help_text='微信收款二维码图片的Base64编码或URL'
    )

    # 支付宝收款码
    alipay_qr_code = models.TextField(
        blank=True,
        verbose_name='支付宝收款码',
        help_text='支付宝收款二维码图片的Base64编码或URL'
    )

    # 联系信息
    contact_phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name='联系电话',
        help_text='收款人联系电话'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )

    class Meta:
        db_table = 'agent_payment_info'
        verbose_name = '代理商收款信息'
        verbose_name_plural = '代理商收款信息'

    def __str__(self):
        return f'{self.agent_user.email} - 收款信息'

    @property
    def has_payment_method(self):
        """检查是否配置了至少一种收款方式"""
        return bool(
            (self.bank_name and self.bank_account and self.account_holder) or
            self.wechat_qr_code or
            self.alipay_qr_code
        )

    @property
    def available_payment_methods(self):
        """获取可用的收款方式列表"""
        methods = []
        if self.bank_name and self.bank_account and self.account_holder:
            methods.append('bank')
        if self.wechat_qr_code:
            methods.append('wechat')
        if self.alipay_qr_code:
            methods.append('alipay')
        return methods


class WithdrawRequest(models.Model):
    """
    提现申请
    """
    STATUS_CHOICES = [
        ('pending', '待审核'),
        ('approved', '已通过'),
        ('rejected', '已拒绝'),
        ('completed', '已打款'),
    ]

    agent_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='withdraw_requests',
        verbose_name='代理商用户'
    )

    # 提现金额信息
    withdraw_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='提现金额',
        help_text='用户申请的提现金额'
    )

    fee_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name='手续费比例',
        help_text='申请时的手续费比例（百分比）'
    )

    fee_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='手续费金额',
        help_text='根据手续费比例计算的手续费金额'
    )

    actual_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='实际到账金额',
        help_text='扣除手续费后的实际到账金额'
    )

    # 状态和审核信息
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='状态'
    )

    # 审核信息
    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_withdraws',
        verbose_name='审核人'
    )

    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='审核时间'
    )

    reject_reason = models.TextField(
        blank=True,
        verbose_name='拒绝原因',
        help_text='审核拒绝时的原因说明'
    )

    # 打款信息
    completed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='completed_withdraws',
        verbose_name='打款操作人'
    )

    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='打款时间'
    )

    payment_reference = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='打款凭证',
        help_text='银行转账凭证号或其他支付凭证'
    )

    # 收款信息快照（申请时的收款信息）
    payment_method = models.CharField(
        max_length=20,
        verbose_name='收款方式',
        help_text='bank/wechat/alipay'
    )

    payment_info_snapshot = models.JSONField(
        verbose_name='收款信息快照',
        help_text='申请时的收款信息快照，防止后续修改影响审核'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='申请时间'
    )

    class Meta:
        db_table = 'withdraw_request'
        verbose_name = '提现申请'
        verbose_name_plural = '提现申请'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['agent_user', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['reviewed_by']),
        ]
        constraints = [
            # 确保同一代理商同时只能有一个待审核的申请
            models.UniqueConstraint(
                fields=['agent_user'],
                condition=models.Q(status='pending'),
                name='unique_pending_withdraw_per_agent'
            )
        ]

    def __str__(self):
        return f'{self.agent_user.email} - ¥{self.withdraw_amount} - {self.get_status_display()}'

    def save(self, *args, **kwargs):
        # 自动计算手续费和实际到账金额
        if self.withdraw_amount and self.fee_rate is not None:
            self.fee_amount = self.withdraw_amount * (self.fee_rate / 100)
            self.actual_amount = self.withdraw_amount - self.fee_amount
        super().save(*args, **kwargs)
