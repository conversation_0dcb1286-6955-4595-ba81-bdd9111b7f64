from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import AgentLevel, UserAgent, CommissionRecord, BillRecord, RechargeRecord, AgentPaymentInfo, WithdrawRequest


@admin.register(AgentLevel)
class AgentLevelAdmin(admin.ModelAdmin):
    """代理商等级管理"""
    list_display = ['name', 'fee_display', 'commission_rate_display', 'sort_order', 'is_active', 'user_count']
    list_filter = ['is_active']
    search_fields = ['name', 'service_content']
    ordering = ['sort_order']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'fee', 'commission_rate', 'sort_order', 'is_active')
        }),
        ('服务内容', {
            'fields': ('service_content',),
            'description': '每行输入一项服务内容，将在前端以列表形式显示'
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def fee_display(self, obj):
        """代理费显示"""
        return f"¥{obj.fee:,.0f}"
    fee_display.short_description = '代理费'

    def commission_rate_display(self, obj):
        """分成比例显示"""
        return f"{obj.commission_rate}%"
    commission_rate_display.short_description = '分成比例'

    def user_count(self, obj):
        """代理商数量"""
        count = obj.user_agents.filter(is_active=True).count()
        return format_html('<span style="color: #007cba; font-weight: bold;">{}</span>', count)
    user_count.short_description = '代理商数量'


@admin.register(UserAgent)
class UserAgentAdmin(admin.ModelAdmin):
    """用户代理关系管理"""
    list_display = [
        'user_email', 'agent_level', 'agent_mode_display', 'effective_commission_rate_display',
        'domain', 'credit_info_display', 'is_active', 'commission_count', 'total_commission', 'created_at'
    ]
    list_filter = ['agent_level', 'agent_mode', 'is_active', 'created_at', 'updated_at']
    search_fields = ['user__email', 'domain', 'agent_level__name']
    raw_id_fields = ['user']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('用户信息', {
            'fields': ('user',)
        }),
        ('代理设置', {
            'fields': ('agent_level', 'agent_mode', 'custom_commission_rate', 'is_active')
        }),
        ('预付费设置', {
            'fields': ('total_credit', 'remaining_credit'),
            'classes': ('collapse',)
        }),
        ('域名绑定', {
            'fields': ('domain',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def user_email(self, obj):
        """用户邮箱"""
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def agent_mode_display(self, obj):
        """代理模式显示"""
        mode_colors = {
            'commission': '#28a745',
            'prepaid': '#007bff',
        }
        mode_names = {
            'commission': '分成模式',
            'prepaid': '预付费模式',
        }
        color = mode_colors.get(obj.agent_mode, '#6c757d')
        name = mode_names.get(obj.agent_mode, obj.agent_mode)
        return format_html('<span style="color: {}; font-weight: bold;">{}</span>', color, name)
    agent_mode_display.short_description = '代理模式'

    def credit_info_display(self, obj):
        """额度信息显示"""
        if obj.agent_mode == 'prepaid':
            return format_html(
                '<span style="color: #007cba;">剩余: ¥{}</span><br><span style="color: #6c757d; font-size: 11px;">总额: ¥{}</span>',
                f'{obj.remaining_credit:.2f}',
                f'{obj.total_credit:.2f}'
            )
        return format_html('<span style="color: #6c757d;">-</span>')
    credit_info_display.short_description = '额度信息'

    def effective_commission_rate_display(self, obj):
        """有效分成比例"""
        rate = obj.effective_commission_rate
        if obj.custom_commission_rate:
            return format_html('<span style="color: #dc3545; font-weight: bold;">{}%</span>', rate)
        return f"{rate}%"
    effective_commission_rate_display.short_description = '有效分成比例'

    def commission_count(self, obj):
        """分成记录数"""
        count = obj.user.agent_commissions.count()
        return format_html('<span style="color: #007cba;">{}</span>', count)
    commission_count.short_description = '分成记录数'

    def total_commission(self, obj):
        """总分成金额"""
        from django.db.models import Sum
        total = obj.user.agent_commissions.filter(status='settled').aggregate(
            total=Sum('commission_amount')
        )['total'] or 0
        return format_html('<span style="color: #28a745; font-weight: bold;">¥{}</span>', f'{total:.2f}')
    total_commission.short_description = '总分成金额'


@admin.register(CommissionRecord)
class CommissionRecordAdmin(admin.ModelAdmin):
    """分成记录管理"""
    list_display = [
        'id', 'agent_user_email', 'consumer_user_email', 'order_id',
        'order_amount_display', 'commission_rate_display', 'commission_amount_display',
        'status_display', 'created_at', 'settled_at'
    ]
    list_filter = ['status', 'created_at', 'settled_at']
    search_fields = ['agent_user__email', 'consumer_user__email', 'order_id']
    raw_id_fields = ['agent_user', 'consumer_user']
    readonly_fields = ['created_at', 'settled_at']
    ordering = ['-created_at']

    fieldsets = (
        ('用户信息', {
            'fields': ('agent_user', 'consumer_user')
        }),
        ('订单信息', {
            'fields': ('order_id', 'order_amount')
        }),
        ('分成信息', {
            'fields': ('commission_rate', 'commission_amount', 'status')
        }),
        ('时间信息', {
            'fields': ('created_at', 'settled_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_settled', 'mark_as_cancelled']

    def agent_user_email(self, obj):
        """代理商邮箱"""
        return obj.agent_user.email
    agent_user_email.short_description = '代理商邮箱'

    def consumer_user_email(self, obj):
        """消费者邮箱"""
        return obj.consumer_user.email
    consumer_user_email.short_description = '消费者邮箱'

    def order_amount_display(self, obj):
        """订单金额显示"""
        return format_html('<span style="color: #007cba;">¥{}</span>', f'{obj.order_amount:.2f}')
    order_amount_display.short_description = '订单金额'

    def commission_rate_display(self, obj):
        """分成比例显示"""
        return f"{obj.commission_rate}%"
    commission_rate_display.short_description = '分成比例'

    def commission_amount_display(self, obj):
        """分成金额显示"""
        return format_html('<span style="color: #28a745; font-weight: bold;">¥{}</span>', f'{obj.commission_amount:.2f}')
    commission_amount_display.short_description = '分成金额'

    def status_display(self, obj):
        """状态显示"""
        colors = {
            'pending': '#ffc107',
            'settled': '#28a745',
            'cancelled': '#dc3545',
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = '状态'

    def mark_as_settled(self, request, queryset):
        """标记为已结算"""
        updated = queryset.filter(status='pending').update(
            status='settled',
            settled_at=timezone.now()
        )
        self.message_user(request, f'成功标记 {updated} 条记录为已结算')
    mark_as_settled.short_description = '标记为已结算'

    def mark_as_cancelled(self, request, queryset):
        """标记为已取消"""
        updated = queryset.filter(status='pending').update(status='cancelled')
        self.message_user(request, f'成功标记 {updated} 条记录为已取消')
    mark_as_cancelled.short_description = '标记为已取消'


@admin.register(BillRecord)
class BillRecordAdmin(admin.ModelAdmin):
    """账单记录管理（预付费模式）"""
    list_display = [
        'id', 'agent_user_email', 'consumer_user_email', 'order_id',
        'order_amount_display', 'remaining_credit_display', 'created_at'
    ]
    list_filter = ['created_at']
    search_fields = ['agent_user__email', 'consumer_user__email', 'order_id']
    raw_id_fields = ['agent_user', 'consumer_user']
    readonly_fields = ['created_at']
    ordering = ['-created_at']

    fieldsets = (
        ('用户信息', {
            'fields': ('agent_user', 'consumer_user')
        }),
        ('订单信息', {
            'fields': ('order_id', 'order_amount')
        }),
        ('额度信息', {
            'fields': ('remaining_credit',)
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def agent_user_email(self, obj):
        """代理商邮箱"""
        return obj.agent_user.email
    agent_user_email.short_description = '代理商邮箱'

    def consumer_user_email(self, obj):
        """消费者邮箱"""
        return obj.consumer_user.email
    consumer_user_email.short_description = '消费者邮箱'

    def order_amount_display(self, obj):
        """订单金额显示"""
        return format_html('<span style="color: #dc3545; font-weight: bold;">-¥{}</span>', f'{obj.order_amount:.2f}')
    order_amount_display.short_description = '扣除金额'

    def remaining_credit_display(self, obj):
        """剩余额度显示"""
        return format_html('<span style="color: #007cba;">¥{}</span>', f'{obj.remaining_credit:.2f}')
    remaining_credit_display.short_description = '剩余额度'


@admin.register(RechargeRecord)
class RechargeRecordAdmin(admin.ModelAdmin):
    """充值记录管理（预付费模式）"""
    list_display = [
        'id', 'agent_user_email', 'recharge_amount_display', 'credit_amount_display',
        'commission_rate_display', 'status_display', 'created_at', 'paid_at'
    ]
    list_filter = ['status', 'created_at', 'paid_at']
    search_fields = ['agent_user__email', 'payment_code']
    raw_id_fields = ['agent_user']
    readonly_fields = ['created_at', 'paid_at', 'payment_code']
    ordering = ['-created_at']

    fieldsets = (
        ('用户信息', {
            'fields': ('agent_user',)
        }),
        ('充值信息', {
            'fields': ('recharge_amount', 'credit_amount', 'commission_rate')
        }),
        ('支付信息', {
            'fields': ('status', 'payment_code')
        }),
        ('时间信息', {
            'fields': ('created_at', 'paid_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_paid', 'mark_as_cancelled']

    def agent_user_email(self, obj):
        """代理商邮箱"""
        return obj.agent_user.email
    agent_user_email.short_description = '代理商邮箱'

    def recharge_amount_display(self, obj):
        """充值金额显示"""
        return format_html('<span style="color: #007cba; font-weight: bold;">¥{}</span>', f'{obj.recharge_amount:.2f}')
    recharge_amount_display.short_description = '充值金额'

    def credit_amount_display(self, obj):
        """获得额度显示"""
        return format_html('<span style="color: #28a745; font-weight: bold;">¥{}</span>', f'{obj.credit_amount:.2f}')
    credit_amount_display.short_description = '获得额度'

    def commission_rate_display(self, obj):
        """分成比例显示"""
        return f"{obj.commission_rate}%"
    commission_rate_display.short_description = '分成比例'

    def status_display(self, obj):
        """状态显示"""
        colors = {
            'pending': '#ffc107',
            'paid': '#28a745',
            'cancelled': '#dc3545',
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = '状态'

    def mark_as_paid(self, request, queryset):
        """标记为已支付"""
        updated = queryset.filter(status='pending').update(
            status='paid',
            paid_at=timezone.now()
        )
        self.message_user(request, f'成功标记 {updated} 条记录为已支付')
    mark_as_paid.short_description = '标记为已支付'

    def mark_as_cancelled(self, request, queryset):
        """标记为已取消"""
        updated = queryset.filter(status='pending').update(status='cancelled')
        self.message_user(request, f'成功标记 {updated} 条记录为已取消')
    mark_as_cancelled.short_description = '标记为已取消'


@admin.register(AgentPaymentInfo)
class AgentPaymentInfoAdmin(admin.ModelAdmin):
    """代理商收款信息管理"""
    list_display = [
        'id', 'agent_user_email', 'payment_methods_display',
        'contact_phone', 'created_at', 'updated_at'
    ]
    list_filter = ['created_at', 'updated_at']
    search_fields = ['agent_user__email', 'account_holder', 'contact_phone']
    raw_id_fields = ['agent_user']
    readonly_fields = ['created_at', 'updated_at', 'has_payment_method', 'available_payment_methods']
    ordering = ['-updated_at']

    fieldsets = (
        ('代理商信息', {
            'fields': ('agent_user',)
        }),
        ('银行卡信息', {
            'fields': ('bank_name', 'bank_account', 'account_holder'),
            'description': '银行卡收款信息，需要完整填写才能使用'
        }),
        ('电子收款码', {
            'fields': ('wechat_qr_code', 'alipay_qr_code'),
            'description': '微信和支付宝收款二维码'
        }),
        ('联系信息', {
            'fields': ('contact_phone',)
        }),
        ('状态信息', {
            'fields': ('has_payment_method', 'available_payment_methods'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def agent_user_email(self, obj):
        """代理商邮箱"""
        return obj.agent_user.email
    agent_user_email.short_description = '代理商邮箱'

    def payment_methods_display(self, obj):
        """收款方式显示"""
        methods = obj.available_payment_methods
        if not methods:
            return format_html('<span style="color: #dc3545;">未配置</span>')

        method_names = {
            'bank': '银行卡',
            'wechat': '微信',
            'alipay': '支付宝'
        }

        method_list = [method_names.get(m, m) for m in methods]
        return format_html(
            '<span style="color: #28a745;">{}</span>',
            '、'.join(method_list)
        )
    payment_methods_display.short_description = '收款方式'


@admin.register(WithdrawRequest)
class WithdrawRequestAdmin(admin.ModelAdmin):
    """提现申请管理"""
    list_display = [
        'id', 'agent_user_email', 'withdraw_amount_display', 'fee_amount_display',
        'actual_amount_display', 'status_display', 'payment_method_display',
        'created_at', 'reviewed_at', 'completed_at'
    ]
    list_filter = ['status', 'payment_method', 'created_at', 'reviewed_at', 'completed_at']
    search_fields = ['agent_user__email', 'payment_reference']
    raw_id_fields = ['agent_user', 'reviewed_by', 'completed_by']
    readonly_fields = [
        'created_at', 'fee_amount', 'actual_amount', 'payment_info_snapshot'
    ]
    ordering = ['-created_at']

    fieldsets = (
        ('申请信息', {
            'fields': ('agent_user', 'withdraw_amount', 'fee_rate', 'fee_amount', 'actual_amount')
        }),
        ('收款信息', {
            'fields': ('payment_method', 'payment_info_snapshot')
        }),
        ('审核信息', {
            'fields': ('status', 'reviewed_by', 'reviewed_at', 'reject_reason')
        }),
        ('打款信息', {
            'fields': ('completed_by', 'completed_at', 'payment_reference')
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    actions = ['approve_requests', 'reject_requests', 'complete_requests']

    def agent_user_email(self, obj):
        """代理商邮箱"""
        return obj.agent_user.email
    agent_user_email.short_description = '代理商邮箱'

    def withdraw_amount_display(self, obj):
        """提现金额显示"""
        return format_html('<span style="color: #007cba; font-weight: bold;">¥{}</span>', f'{obj.withdraw_amount:.2f}')
    withdraw_amount_display.short_description = '提现金额'

    def fee_amount_display(self, obj):
        """手续费显示"""
        return format_html('<span style="color: #ffc107;">¥{}</span>', f'{obj.fee_amount:.2f}')
    fee_amount_display.short_description = '手续费'

    def actual_amount_display(self, obj):
        """实际到账金额显示"""
        return format_html('<span style="color: #28a745; font-weight: bold;">¥{}</span>', f'{obj.actual_amount:.2f}')
    actual_amount_display.short_description = '实际到账'

    def status_display(self, obj):
        """状态显示"""
        colors = {
            'pending': '#ffc107',
            'approved': '#17a2b8',
            'rejected': '#dc3545',
            'completed': '#28a745',
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = '状态'

    def payment_method_display(self, obj):
        """收款方式显示"""
        method_names = {
            'bank': '银行卡',
            'wechat': '微信',
            'alipay': '支付宝'
        }
        return method_names.get(obj.payment_method, obj.payment_method)
    payment_method_display.short_description = '收款方式'

    def approve_requests(self, request, queryset):
        """批量通过申请"""
        updated = queryset.filter(status='pending').update(
            status='approved',
            reviewed_by=request.user,
            reviewed_at=timezone.now()
        )
        self.message_user(request, f'成功通过 {updated} 条提现申请')
    approve_requests.short_description = '批量通过申请'

    def reject_requests(self, request, queryset):
        """批量拒绝申请"""
        updated = queryset.filter(status='pending').update(
            status='rejected',
            reviewed_by=request.user,
            reviewed_at=timezone.now(),
            reject_reason='批量拒绝操作'
        )
        self.message_user(request, f'成功拒绝 {updated} 条提现申请')
    reject_requests.short_description = '批量拒绝申请'

    def complete_requests(self, request, queryset):
        """批量完成打款"""
        updated = queryset.filter(status='approved').update(
            status='completed',
            completed_by=request.user,
            completed_at=timezone.now()
        )
        self.message_user(request, f'成功完成 {updated} 条提现打款')
    complete_requests.short_description = '批量完成打款'
