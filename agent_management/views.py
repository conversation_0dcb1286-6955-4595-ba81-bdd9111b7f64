from django.db.models import Q, Sum, Count, Case, When, Value, IntegerField
from django.db import models
from django.utils import timezone
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser, AllowAny, IsA<PERSON>enticated
from datetime import datetime, timedelta
from .models import AgentLevel, UserAgent, CommissionRecord, RechargeRecord, BillRecord, AgentPaymentInfo, WithdrawRequest
from .serializers import (
    AgentLevelSerializer, UserAgentSerializer, CommissionRecordSerializer,
    CommissionStatsSerializer, DomainCheckSerializer, AgentInfoSerializer,
    RechargeRecordSerializer, BillRecordSerializer, RechargeRequestSerializer,
    AgentPaymentInfoSerializer, WithdrawRequestSerializer, WithdrawRequestCreateSerializer
)
from system_settings.models import SystemSettings
from authentication.models import User


class AgentLevelViewSet(viewsets.ModelViewSet):
    """代理商等级管理视图集"""
    queryset = AgentLevel.objects.all()
    serializer_class = AgentLevelSerializer
    permission_classes = [IsAdminUser]

    def get_queryset(self):
        queryset = super().get_queryset()
        is_active = self.request.query_params.get('is_active')

        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset


class UserAgentViewSet(viewsets.ModelViewSet):
    """用户代理关系管理视图集"""
    queryset = UserAgent.objects.select_related('user', 'agent_level').all()
    serializer_class = UserAgentSerializer
    permission_classes = [IsAdminUser]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        agent_level_id = self.request.query_params.get('agent_level_id')
        is_active = self.request.query_params.get('is_active')

        if search:
            queryset = queryset.filter(
                Q(user__email__icontains=search) |
                Q(domain__icontains=search)
            )

        if agent_level_id:
            queryset = queryset.filter(agent_level_id=agent_level_id)

        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset


class CommissionRecordViewSet(viewsets.ReadOnlyModelViewSet):
    """分成记录视图集（只读）"""
    queryset = CommissionRecord.objects.select_related('agent_user', 'consumer_user').all()
    serializer_class = CommissionRecordSerializer
    permission_classes = [IsAdminUser]

    def get_queryset(self):
        queryset = super().get_queryset()
        agent_user_id = self.request.query_params.get('agent_user_id')
        consumer_user_id = self.request.query_params.get('consumer_user_id')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        status_filter = self.request.query_params.get('status')

        if agent_user_id:
            queryset = queryset.filter(agent_user_id=agent_user_id)

        if consumer_user_id:
            queryset = queryset.filter(consumer_user_id=consumer_user_id)

        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=start_date)
            except ValueError:
                pass

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=end_date)
            except ValueError:
                pass

        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset


@api_view(['GET'])
@permission_classes([IsAdminUser])
def check_domain_availability(request):
    """检查域名可用性"""
    domain = request.query_params.get('domain')
    if not domain:
        return Response(
            {'error': '域名参数不能为空'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # 检查域名是否已被使用
    is_available = not UserAgent.objects.filter(domain=domain).exists()

    serializer = DomainCheckSerializer({
        'domain': domain,
        'available': is_available
    })

    return Response({
        'success': True,
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([IsAdminUser])
def get_commission_stats(request, agent_user_id):
    """获取代理商分成统计"""
    try:
        # 验证代理商是否存在
        agent = UserAgent.objects.get(user_id=agent_user_id, is_active=True)
    except UserAgent.DoesNotExist:
        return Response(
            {'error': '代理商不存在'},
            status=status.HTTP_404_NOT_FOUND
        )

    # 获取分成记录
    records = CommissionRecord.objects.filter(
        agent_user_id=agent_user_id,
        status='settled'
    )

    # 计算总分成
    total_commission = records.aggregate(
        total=Sum('commission_amount')
    )['total'] or 0

    # 计算本月分成
    now = timezone.now()
    month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    this_month_records = records.filter(created_at__gte=month_start)
    this_month_commission = this_month_records.aggregate(
        total=Sum('commission_amount')
    )['total'] or 0

    # 计算订单数量
    total_orders = records.count()

    # 计算活跃用户数（本月有消费的用户）
    active_users = this_month_records.values('consumer_user').distinct().count()

    # 获取最近7天的分成记录
    week_ago = now - timedelta(days=7)
    recent_records = records.filter(created_at__gte=week_ago)

    # 按日期分组统计
    daily_stats = []
    for i in range(7):
        date = (now - timedelta(days=i)).date()
        day_records = recent_records.filter(created_at__date=date)
        daily_commission = day_records.aggregate(
            total=Sum('commission_amount')
        )['total'] or 0
        daily_orders = day_records.count()

        daily_stats.append({
            'date': date.strftime('%Y-%m-%d'),
            'commission': float(daily_commission),
            'orders': daily_orders
        })

    stats_data = {
        'total_commission': float(total_commission),
        'this_month_commission': float(this_month_commission),
        'total_orders': total_orders,
        'active_users': active_users,
        'commission_records': daily_stats
    }

    serializer = CommissionStatsSerializer(stats_data)

    return Response({
        'success': True,
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_agent_dashboard(request):
    """获取代理商仪表板数据"""
    try:
        # 检查当前用户是否为代理商
        try:
            user_agent = UserAgent.objects.get(user=request.user, is_active=True)
        except UserAgent.DoesNotExist:
            return Response(
                {'error': '您不是活跃的代理商'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 获取代理商信息
        agent_info = {
            'id': user_agent.id,
            'user': {
                'id': request.user.id,
                'email': request.user.email,
                'points': request.user.points
            },
            'agent_level': {
                'id': user_agent.agent_level.id,
                'name': user_agent.agent_level.name,
                'commission_rate': user_agent.agent_level.commission_rate,
                'description': user_agent.agent_level.description
            },
            'agent_mode': user_agent.agent_mode,
            'custom_commission_rate': user_agent.custom_commission_rate,
            'total_credit': float(user_agent.total_credit),
            'remaining_credit': float(user_agent.remaining_credit),
            'domain': user_agent.domain,
            'is_active': user_agent.is_active
        }

        # 获取统计数据
        # 旗下用户数量
        total_users = User.objects.filter(referred_by_agent=request.user).count()

        # 活跃用户数量（本月有消费的用户）
        from django.utils import timezone
        from datetime import timedelta
        now = timezone.now()
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        active_users = User.objects.filter(
            referred_by_agent=request.user,
            consumer_commissions__created_at__gte=month_start
        ).distinct().count()

        # 分成记录统计
        commission_records = CommissionRecord.objects.filter(agent_user=request.user)

        # 总营收（所有推荐用户的订单金额总和）
        total_revenue = commission_records.aggregate(
            total=Sum('order_amount')
        )['total'] or 0

        # 总分润
        total_commission = commission_records.aggregate(
            total=Sum('commission_amount')
        )['total'] or 0

        # 本月分润
        this_month_commission = commission_records.filter(
            created_at__gte=month_start
        ).aggregate(
            total=Sum('commission_amount')
        )['total'] or 0

        # 根据代理模式计算余额
        if user_agent.agent_mode == 'commission':
            # 分成模式：余额 = 所有分成记录总和 - 所有已成功打款的提现记录总和
            available_balance = calculate_agent_balance(request.user)

            # 待处理提现金额（待审核 + 已通过）
            pending_withdraw = WithdrawRequest.objects.filter(
                agent_user=request.user,
                status__in=['pending', 'approved']
            ).aggregate(
                total=Sum('withdraw_amount')
            )['total'] or 0
        else:
            # 预付费模式：使用原有逻辑
            available_balance = float(user_agent.remaining_credit)
            pending_withdraw = 0

        stats = {
            'total_users': total_users,
            'active_users': active_users,
            'total_revenue': float(total_revenue),
            'total_commission': float(total_commission),
            'available_balance': float(available_balance),
            'pending_withdraw': float(pending_withdraw),
            'this_month_commission': float(this_month_commission)
        }

        # 获取最近的分成记录
        recent_commissions = commission_records.order_by('-created_at')[:10]
        commission_serializer = CommissionRecordSerializer(recent_commissions, many=True)

        dashboard_data = {
            'agent_info': agent_info,
            'stats': stats,
            'recent_commissions': commission_serializer.data
        }

        return Response({
            'success': True,
            'data': dashboard_data
        })

    except Exception as e:
        return Response(
            {'error': f'获取仪表板数据失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_my_commission_records(request):
    """获取当前代理商的分成记录"""
    try:
        # 检查当前用户是否为代理商
        try:
            user_agent = UserAgent.objects.get(user=request.user, is_active=True)
        except UserAgent.DoesNotExist:
            return Response(
                {'error': '您不是活跃的代理商'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 获取查询参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        status_filter = request.query_params.get('status')

        # 构建查询集
        queryset = CommissionRecord.objects.filter(agent_user=request.user)

        # 应用过滤器
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=start_date)
            except ValueError:
                pass

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=end_date)
            except ValueError:
                pass

        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 排序
        queryset = queryset.order_by('-created_at')

        # 分页
        from django.core.paginator import Paginator
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 10)

        try:
            page = int(page)
            page_size = int(page_size)
        except ValueError:
            page = 1
            page_size = 10

        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # 序列化数据
        serializer = CommissionRecordSerializer(page_obj.object_list, many=True)

        # 构建分页响应
        response_data = {
            'results': serializer.data,
            'count': paginator.count,
            'next': page_obj.has_next(),
            'previous': page_obj.has_previous(),
            'current_page': page,
            'total_pages': paginator.num_pages
        }

        return Response({
            'success': True,
            'data': response_data
        })

    except Exception as e:
        return Response(
            {'error': f'获取分成记录失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def get_agent_by_domain(request):
    """根据域名获取代理商信息（公共接口）"""
    domain = request.query_params.get('domain')
    if not domain:
        return Response(
            {'error': '域名参数不能为空'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        user_agent = UserAgent.objects.select_related('agent_level').get(
            domain=domain,
            is_active=True
        )

        agent_info = {
            'agent_user_id': user_agent.user_id,
            'agent_level': AgentLevelSerializer(user_agent.agent_level).data,
            'custom_commission_rate': user_agent.custom_commission_rate
        }

        serializer = AgentInfoSerializer(agent_info)

        return Response({
            'success': True,
            'data': serializer.data
        })

    except UserAgent.DoesNotExist:
        return Response(
            {'error': '未找到对应的代理商'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_recharge_order(request):
    """创建充值订单"""
    try:
        # 检查当前用户是否为预付费模式代理商
        try:
            user_agent = UserAgent.objects.get(user=request.user, is_active=True, agent_mode='prepaid')
        except UserAgent.DoesNotExist:
            return Response(
                {'error': '您不是预付费模式的代理商'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 验证请求数据
        serializer = RechargeRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {'error': '请求数据无效', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        recharge_amount = serializer.validated_data['recharge_amount']

        # 充值金额直接作为获得的额度（不再进行分成比例换算）
        commission_rate = user_agent.effective_commission_rate
        credit_amount = recharge_amount

        # 创建充值记录
        recharge_record = RechargeRecord.objects.create(
            agent_user=request.user,
            recharge_amount=recharge_amount,
            credit_amount=credit_amount,
            commission_rate=commission_rate,
            status='pending'
        )

        # 生成支付码（这里简化处理，实际应该调用支付接口）
        import uuid
        payment_code = f"RECHARGE_{recharge_record.id}_{uuid.uuid4().hex[:8]}"
        recharge_record.payment_code = payment_code
        recharge_record.save()

        return Response({
            'success': True,
            'data': {
                'recharge_id': recharge_record.id,
                'payment_code': payment_code,
                'recharge_amount': float(recharge_amount),
                'credit_amount': float(credit_amount),
                'commission_rate': float(commission_rate)
            }
        })

    except Exception as e:
        return Response(
            {'error': f'创建充值订单失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def confirm_recharge_payment(request, recharge_id):
    """确认充值支付（模拟支付成功）"""
    import logging
    from system_settings.models import SystemSettings

    logger = logging.getLogger(__name__)

    try:
        # 检查模拟支付开关
        if not SystemSettings.is_mock_payment_enabled():
            logger.warning(f"模拟支付被禁用，拒绝充值模拟支付请求，用户: {request.user.email}, IP: {request.META.get('REMOTE_ADDR')}")
            return Response({
                'success': False,
                'message': '模拟支付功能已被禁用'
            }, status=status.HTTP_403_FORBIDDEN)

        # 获取充值记录
        try:
            recharge_record = RechargeRecord.objects.get(
                id=recharge_id,
                agent_user=request.user,
                status='pending'
            )
        except RechargeRecord.DoesNotExist:
            return Response(
                {'error': '充值记录不存在或已处理'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 更新充值记录状态
        recharge_record.status = 'paid'
        recharge_record.paid_at = timezone.now()
        recharge_record.save()

        # 更新代理商额度
        user_agent = UserAgent.objects.get(user=request.user)
        user_agent.total_credit += recharge_record.credit_amount
        user_agent.remaining_credit += recharge_record.credit_amount
        user_agent.save()

        # 检查并完成等待额度的降AIGC订单
        from reduce_aigc.models import ReduceOrder
        completed_orders = ReduceOrder.complete_pending_orders_for_agent(request.user)

        # 记录模拟支付日志
        logger.info(f"🧪 [模拟支付] 代理充值支付成功 - 充值ID: {recharge_id}, 用户: {request.user.email}, 充值金额: ¥{recharge_record.recharge_amount}, 额度: ¥{recharge_record.credit_amount}, IP: {request.META.get('REMOTE_ADDR')}")

        response_message = '充值成功'
        if completed_orders > 0:
            response_message += f'，已自动完成 {completed_orders} 个等待中的订单'

        return Response({
            'success': True,
            'message': response_message,
            'data': {
                'total_credit': float(user_agent.total_credit),
                'remaining_credit': float(user_agent.remaining_credit),
                'completed_orders': completed_orders
            }
        })

    except Exception as e:
        return Response(
            {'error': f'确认充值失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_recharge_records(request):
    """获取充值记录"""
    try:
        # 检查当前用户是否为预付费模式代理商
        try:
            user_agent = UserAgent.objects.get(user=request.user, is_active=True, agent_mode='prepaid')
        except UserAgent.DoesNotExist:
            return Response(
                {'error': '您不是预付费模式的代理商'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 获取充值记录
        records = RechargeRecord.objects.filter(agent_user=request.user).order_by('-created_at')

        # 分页处理
        from django.core.paginator import Paginator
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 10)

        paginator = Paginator(records, page_size)
        page_obj = paginator.get_page(page)

        serializer = RechargeRecordSerializer(page_obj.object_list, many=True)

        return Response({
            'success': True,
            'data': {
                'results': serializer.data,
                'count': paginator.count,
                'page': int(page),
                'page_size': int(page_size),
                'total_pages': paginator.num_pages
            }
        })

    except Exception as e:
        return Response(
            {'error': f'获取充值记录失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_bill_records(request):
    """获取账单记录（预付费模式）"""
    try:
        # 检查当前用户是否为预付费模式代理商
        try:
            user_agent = UserAgent.objects.get(user=request.user, is_active=True, agent_mode='prepaid')
        except UserAgent.DoesNotExist:
            return Response(
                {'error': '您不是预付费模式的代理商'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 获取账单记录
        records = BillRecord.objects.filter(agent_user=request.user).order_by('-created_at')

        # 分页处理
        from django.core.paginator import Paginator
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 10)

        paginator = Paginator(records, page_size)
        page_obj = paginator.get_page(page)

        serializer = BillRecordSerializer(page_obj.object_list, many=True)

        return Response({
            'success': True,
            'data': {
                'results': serializer.data,
                'count': paginator.count,
                'page': int(page),
                'page_size': int(page_size),
                'total_pages': paginator.num_pages
            }
        })

    except Exception as e:
        return Response(
            {'error': f'获取账单记录失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def deduct_agent_credit(agent_user, consumer_user, order_id, order_amount):
    """
    扣除代理商额度（预付费模式）
    注意：允许额度为负数

    Args:
        agent_user: 代理商用户
        consumer_user: 消费用户
        order_id: 订单ID
        order_amount: 订单金额

    Returns:
        tuple: (success, message, remaining_credit)
    """
    try:
        # 获取代理商信息
        user_agent = UserAgent.objects.get(user=agent_user, is_active=True, agent_mode='prepaid')

        # 计算总部分润比例和金额
        agent_commission_rate = user_agent.effective_commission_rate
        headquarters_commission_rate = 100 - agent_commission_rate
        headquarters_commission = order_amount * (headquarters_commission_rate / 100)

        # 根据总部分润比例扣除额度（允许负数）
        user_agent.remaining_credit -= headquarters_commission
        user_agent.save()

        # 创建账单记录
        bill_record = BillRecord.objects.create(
            agent_user=agent_user,
            consumer_user=consumer_user,
            order_id=order_id,
            order_amount=order_amount,
            headquarters_commission=headquarters_commission,
            remaining_credit=user_agent.remaining_credit
        )

        return True, '额度扣除成功', float(user_agent.remaining_credit)

    except UserAgent.DoesNotExist:
        return False, '代理商不存在或不是预付费模式', 0
    except Exception as e:
        return False, f'扣除额度失败: {str(e)}', 0


def check_agent_credit_sufficient(agent_user, order_amount):
    """
    检查代理商额度是否充足

    Args:
        agent_user: 代理商用户
        order_amount: 订单金额

    Returns:
        tuple: (sufficient, remaining_credit)
    """
    try:
        user_agent = UserAgent.objects.get(user=agent_user, is_active=True, agent_mode='prepaid')
        return user_agent.remaining_credit >= order_amount, float(user_agent.remaining_credit)
    except UserAgent.DoesNotExist:
        return True, 0  # 非预付费模式代理商，不需要检查额度


# ==================== 代理商端提现相关API ====================

@api_view(['GET', 'POST', 'PUT'])
@permission_classes([IsAuthenticated])
def manage_payment_info(request):
    """管理代理商收款信息"""
    try:
        # 检查当前用户是否为代理商
        try:
            user_agent = UserAgent.objects.get(user=request.user, is_active=True, agent_mode='commission')
        except UserAgent.DoesNotExist:
            return Response(
                {'error': '您不是分成模式的代理商'},
                status=status.HTTP_403_FORBIDDEN
            )

        if request.method == 'GET':
            # 获取收款信息
            try:
                payment_info = AgentPaymentInfo.objects.get(agent_user=request.user)
                serializer = AgentPaymentInfoSerializer(payment_info)
                return Response({
                    'success': True,
                    'data': serializer.data
                })
            except AgentPaymentInfo.DoesNotExist:
                return Response({
                    'success': True,
                    'data': None
                })

        elif request.method in ['POST', 'PUT']:
            # 创建或更新收款信息
            try:
                payment_info = AgentPaymentInfo.objects.get(agent_user=request.user)
                serializer = AgentPaymentInfoSerializer(payment_info, data=request.data, partial=True)
            except AgentPaymentInfo.DoesNotExist:
                serializer = AgentPaymentInfoSerializer(data=request.data)

            if serializer.is_valid():
                if hasattr(serializer, 'instance') and serializer.instance:
                    # 更新现有记录
                    payment_info = serializer.save()
                else:
                    # 创建新记录
                    payment_info = serializer.save(agent_user=request.user)

                return Response({
                    'success': True,
                    'message': '收款信息保存成功',
                    'data': AgentPaymentInfoSerializer(payment_info).data
                })
            else:
                return Response({
                    'success': False,
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response(
            {'error': f'操作失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_withdraw_request(request):
    """创建提现申请"""
    try:
        # 检查当前用户是否为分成模式代理商
        try:
            user_agent = UserAgent.objects.get(user=request.user, is_active=True, agent_mode='commission')
        except UserAgent.DoesNotExist:
            return Response(
                {'error': '您不是分成模式的代理商'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 检查是否已有待审核的申请
        existing_request = WithdrawRequest.objects.filter(
            agent_user=request.user,
            status='pending'
        ).first()

        if existing_request:
            return Response(
                {'error': '您已有待审核的提现申请，请等待审核完成后再提交新申请'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 验证请求数据
        serializer = WithdrawRequestCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        withdraw_amount = serializer.validated_data['withdraw_amount']
        payment_method = serializer.validated_data['payment_method']

        # 获取系统配置
        withdraw_settings = SystemSettings.get_withdraw_settings()
        min_withdraw_amount = withdraw_settings['min_withdraw_amount']
        fee_rate = withdraw_settings['withdraw_fee_rate']

        # 验证最低提现金额
        if withdraw_amount < min_withdraw_amount:
            return Response(
                {'error': f'提现金额不能少于{min_withdraw_amount}元'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 计算当前可用余额
        available_balance = calculate_agent_balance(request.user)

        # 验证余额是否充足
        if withdraw_amount > available_balance:
            return Response(
                {'error': f'提现金额不能超过可用余额¥{available_balance:.2f}'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 检查收款信息
        try:
            payment_info = AgentPaymentInfo.objects.get(agent_user=request.user)
            if not payment_info.has_payment_method:
                return Response(
                    {'error': '请先配置收款信息'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 验证选择的收款方式是否可用
            if payment_method not in payment_info.available_payment_methods:
                return Response(
                    {'error': '选择的收款方式不可用，请先配置相应的收款信息'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except AgentPaymentInfo.DoesNotExist:
            return Response(
                {'error': '请先配置收款信息'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 创建收款信息快照
        payment_info_snapshot = {}
        if payment_method == 'bank':
            payment_info_snapshot = {
                'bank_name': payment_info.bank_name,
                'bank_account': payment_info.bank_account,
                'account_holder': payment_info.account_holder,
                'contact_phone': payment_info.contact_phone
            }
        elif payment_method == 'wechat':
            payment_info_snapshot = {
                'wechat_qr_code': payment_info.wechat_qr_code,
                'contact_phone': payment_info.contact_phone
            }
        elif payment_method == 'alipay':
            payment_info_snapshot = {
                'alipay_qr_code': payment_info.alipay_qr_code,
                'contact_phone': payment_info.contact_phone
            }

        # 创建提现申请
        withdraw_request = WithdrawRequest.objects.create(
            agent_user=request.user,
            withdraw_amount=withdraw_amount,
            fee_rate=fee_rate,
            payment_method=payment_method,
            payment_info_snapshot=payment_info_snapshot
        )

        return Response({
            'success': True,
            'message': '提现申请提交成功，请等待审核',
            'data': WithdrawRequestSerializer(withdraw_request).data
        })

    except Exception as e:
        return Response(
            {'error': f'提交申请失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_my_withdraw_requests(request):
    """获取当前代理商的提现申请记录"""
    try:
        # 检查当前用户是否为分成模式代理商
        try:
            user_agent = UserAgent.objects.get(user=request.user, is_active=True, agent_mode='commission')
        except UserAgent.DoesNotExist:
            return Response(
                {'error': '您不是分成模式的代理商'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 获取查询参数
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        status_filter = request.query_params.get('status')

        # 构建查询集
        queryset = WithdrawRequest.objects.filter(agent_user=request.user)

        # 应用状态过滤
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 排序
        queryset = queryset.order_by('-created_at')

        # 分页
        total_count = queryset.count()
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        withdraw_requests = queryset[start_index:end_index]

        # 序列化数据
        serializer = WithdrawRequestSerializer(withdraw_requests, many=True)

        # 构建分页响应
        has_next = end_index < total_count
        has_previous = page > 1

        return Response({
            'success': True,
            'data': {
                'results': serializer.data,
                'count': total_count,
                'next': f'?page={page + 1}&page_size={page_size}' if has_next else None,
                'previous': f'?page={page - 1}&page_size={page_size}' if has_previous else None,
                'current_page': page,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })

    except Exception as e:
        return Response(
            {'error': f'获取提现记录失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_withdraw_settings(request):
    """获取提现相关配置"""
    try:
        # 检查当前用户是否为分成模式代理商
        try:
            user_agent = UserAgent.objects.get(user=request.user, is_active=True, agent_mode='commission')
        except UserAgent.DoesNotExist:
            return Response(
                {'error': '您不是分成模式的代理商'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 获取系统配置
        withdraw_settings = SystemSettings.get_withdraw_settings()

        # 计算当前可用余额
        available_balance = calculate_agent_balance(request.user)

        return Response({
            'success': True,
            'data': {
                'min_withdraw_amount': float(withdraw_settings['min_withdraw_amount']),
                'withdraw_fee_rate': float(withdraw_settings['withdraw_fee_rate']),
                'available_balance': available_balance
            }
        })

    except Exception as e:
        return Response(
            {'error': f'获取配置失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def calculate_agent_balance(agent_user):
    """
    计算代理商的可用余额
    余额 = 所有分成记录总和 - 所有已成功打款的提现记录总和
    """
    try:
        # 计算总分成金额
        total_commission = CommissionRecord.objects.filter(
            agent_user=agent_user
        ).aggregate(
            total=Sum('commission_amount')
        )['total'] or 0

        # 计算已成功提现的金额
        total_withdrawn = WithdrawRequest.objects.filter(
            agent_user=agent_user,
            status='completed'
        ).aggregate(
            total=Sum('withdraw_amount')
        )['total'] or 0

        # 计算待审核和已通过的提现金额（这部分余额暂时不可用）
        pending_withdraw = WithdrawRequest.objects.filter(
            agent_user=agent_user,
            status__in=['pending', 'approved']
        ).aggregate(
            total=Sum('withdraw_amount')
        )['total'] or 0

        # 可用余额 = 总分成 - 已提现 - 待处理提现
        available_balance = float(total_commission) - float(total_withdrawn) - float(pending_withdraw)

        return max(0, available_balance)  # 确保余额不为负数

    except Exception as e:
        return 0


# ==================== 管理员端提现审核API ====================

@api_view(['GET'])
@permission_classes([IsAdminUser])
def get_withdraw_requests_admin(request):
    """管理员获取提现申请列表"""
    try:
        # 获取查询参数
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        status_filter = request.query_params.get('status')
        search = request.query_params.get('search')

        # 构建查询集
        queryset = WithdrawRequest.objects.select_related('agent_user', 'reviewed_by', 'completed_by')

        # 应用过滤器
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        if search:
            queryset = queryset.filter(
                Q(agent_user__email__icontains=search) |
                Q(payment_reference__icontains=search)
            )

        # 排序：待审核的在前，然后按创建时间倒序
        queryset = queryset.order_by(
            Case(
                When(status='pending', then=Value(0)),
                When(status='approved', then=Value(1)),
                default=Value(2),
                output_field=IntegerField()
            ),
            '-created_at'
        )

        # 分页
        total_count = queryset.count()
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        withdraw_requests = queryset[start_index:end_index]

        # 序列化数据
        serializer = WithdrawRequestSerializer(withdraw_requests, many=True)

        # 构建分页响应
        has_next = end_index < total_count
        has_previous = page > 1

        return Response({
            'success': True,
            'data': {
                'results': serializer.data,
                'count': total_count,
                'next': f'?page={page + 1}&page_size={page_size}' if has_next else None,
                'previous': f'?page={page - 1}&page_size={page_size}' if has_previous else None,
                'current_page': page,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })

    except Exception as e:
        return Response(
            {'error': f'获取提现申请列表失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAdminUser])
def review_withdraw_request(request, request_id):
    """审核提现申请"""
    try:
        # 获取提现申请
        try:
            withdraw_request = WithdrawRequest.objects.get(id=request_id)
        except WithdrawRequest.DoesNotExist:
            return Response(
                {'error': '提现申请不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 检查申请状态
        if withdraw_request.status != 'pending':
            return Response(
                {'error': f'该申请已被处理，当前状态：{withdraw_request.get_status_display()}'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 验证请求数据
        from .serializers import WithdrawRequestReviewSerializer
        serializer = WithdrawRequestReviewSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        action = serializer.validated_data['action']
        reject_reason = serializer.validated_data.get('reject_reason', '')

        # 使用数据库事务确保数据一致性
        from django.db import transaction

        with transaction.atomic():
            if action == 'approve':
                # 通过申请
                # 再次验证余额（防止并发问题）
                current_balance = calculate_agent_balance(withdraw_request.agent_user)
                if withdraw_request.withdraw_amount > current_balance:
                    return Response(
                        {'error': f'代理商余额不足，当前余额：¥{current_balance:.2f}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # 更新申请状态
                withdraw_request.status = 'approved'
                withdraw_request.reviewed_by = request.user
                withdraw_request.reviewed_at = timezone.now()
                withdraw_request.save()

                message = '提现申请审核通过'

            elif action == 'reject':
                # 拒绝申请
                withdraw_request.status = 'rejected'
                withdraw_request.reviewed_by = request.user
                withdraw_request.reviewed_at = timezone.now()
                withdraw_request.reject_reason = reject_reason
                withdraw_request.save()

                message = '提现申请已拒绝'

        return Response({
            'success': True,
            'message': message,
            'data': WithdrawRequestSerializer(withdraw_request).data
        })

    except Exception as e:
        return Response(
            {'error': f'审核失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAdminUser])
def complete_withdraw_request(request, request_id):
    """完成提现（标记为已打款）"""
    try:
        # 获取提现申请
        try:
            withdraw_request = WithdrawRequest.objects.get(id=request_id)
        except WithdrawRequest.DoesNotExist:
            return Response(
                {'error': '提现申请不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 检查申请状态
        if withdraw_request.status != 'approved':
            return Response(
                {'error': f'只能对已通过的申请进行打款操作，当前状态：{withdraw_request.get_status_display()}'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 验证请求数据
        from .serializers import WithdrawRequestCompleteSerializer
        serializer = WithdrawRequestCompleteSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        payment_reference = serializer.validated_data.get('payment_reference', '')

        # 使用数据库事务确保数据一致性
        from django.db import transaction

        with transaction.atomic():
            # 更新申请状态
            withdraw_request.status = 'completed'
            withdraw_request.completed_by = request.user
            withdraw_request.completed_at = timezone.now()
            withdraw_request.payment_reference = payment_reference
            withdraw_request.save()

        return Response({
            'success': True,
            'message': '提现已完成',
            'data': WithdrawRequestSerializer(withdraw_request).data
        })

    except Exception as e:
        return Response(
            {'error': f'完成提现失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAdminUser])
def get_withdraw_statistics(request):
    """获取提现统计数据"""
    try:
        # 获取时间范围参数
        from datetime import datetime, timedelta
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)  # 默认最近30天

        date_param = request.query_params.get('date_range')
        if date_param == '7d':
            start_date = end_date - timedelta(days=7)
        elif date_param == '30d':
            start_date = end_date - timedelta(days=30)
        elif date_param == '90d':
            start_date = end_date - timedelta(days=90)

        # 基础统计
        total_requests = WithdrawRequest.objects.count()
        pending_requests = WithdrawRequest.objects.filter(status='pending').count()
        approved_requests = WithdrawRequest.objects.filter(status='approved').count()
        completed_requests = WithdrawRequest.objects.filter(status='completed').count()
        rejected_requests = WithdrawRequest.objects.filter(status='rejected').count()

        # 金额统计
        total_amount = WithdrawRequest.objects.aggregate(
            total=Sum('withdraw_amount')
        )['total'] or 0

        completed_amount = WithdrawRequest.objects.filter(
            status='completed'
        ).aggregate(
            total=Sum('withdraw_amount')
        )['total'] or 0

        pending_amount = WithdrawRequest.objects.filter(
            status__in=['pending', 'approved']
        ).aggregate(
            total=Sum('withdraw_amount')
        )['total'] or 0

        # 时间范围内的统计
        period_requests = WithdrawRequest.objects.filter(
            created_at__gte=start_date
        )

        period_count = period_requests.count()
        period_amount = period_requests.aggregate(
            total=Sum('withdraw_amount')
        )['total'] or 0

        return Response({
            'success': True,
            'data': {
                'overview': {
                    'total_requests': total_requests,
                    'pending_requests': pending_requests,
                    'approved_requests': approved_requests,
                    'completed_requests': completed_requests,
                    'rejected_requests': rejected_requests
                },
                'amounts': {
                    'total_amount': float(total_amount),
                    'completed_amount': float(completed_amount),
                    'pending_amount': float(pending_amount)
                },
                'period_stats': {
                    'period_requests': period_count,
                    'period_amount': float(period_amount),
                    'date_range': date_param or '30d'
                }
            }
        })

    except Exception as e:
        return Response(
            {'error': f'获取统计数据失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
