from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

# 创建路由器
router = DefaultRouter()
router.register(r'agent-levels', views.AgentLevelViewSet)
router.register(r'user-agents', views.UserAgentViewSet)
router.register(r'commission-records', views.CommissionRecordViewSet)

# 管理员API路径
admin_urlpatterns = [
    path('', include(router.urls)),
    path('check-domain/', views.check_domain_availability, name='check-domain'),
    path('commission-stats/<int:agent_user_id>/', views.get_commission_stats, name='commission-stats'),
    path('dashboard/', views.get_agent_dashboard, name='agent-dashboard'),
    path('my-commissions/', views.get_my_commission_records, name='my-commissions'),
    # 预付费模式相关API
    path('recharge/create/', views.create_recharge_order, name='create-recharge'),
    path('recharge/<int:recharge_id>/confirm/', views.confirm_recharge_payment, name='confirm-recharge'),
    path('recharge/records/', views.get_recharge_records, name='recharge-records'),
    path('bill/records/', views.get_bill_records, name='bill-records'),
    # 分成模式提现相关API
    path('payment-info/', views.manage_payment_info, name='manage-payment-info'),
    path('withdraw/create/', views.create_withdraw_request, name='create-withdraw'),
    path('withdraw/records/', views.get_my_withdraw_requests, name='my-withdraw-records'),
    path('withdraw/settings/', views.get_withdraw_settings, name='withdraw-settings'),
    # 管理员端提现审核API
    path('withdraw/admin/list/', views.get_withdraw_requests_admin, name='admin-withdraw-list'),
    path('withdraw/admin/<int:request_id>/review/', views.review_withdraw_request, name='admin-withdraw-review'),
    path('withdraw/admin/<int:request_id>/complete/', views.complete_withdraw_request, name='admin-withdraw-complete'),
    path('withdraw/admin/statistics/', views.get_withdraw_statistics, name='admin-withdraw-statistics'),
]

# 公共API路径
public_urlpatterns = [
    path('agent-by-domain/', views.get_agent_by_domain, name='agent-by-domain'),
    path('domain/<str:domain>/', views.get_agent_by_domain, name='agent-by-domain-param'),
]

urlpatterns = [
    path('admin/', include(admin_urlpatterns)),
    path('public/', include(public_urlpatterns)),
]
