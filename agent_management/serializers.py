from rest_framework import serializers
from authentication.models import User
from .models import AgentLevel, UserAgent, CommissionRecord, RechargeRecord, BillRecord, AgentPaymentInfo, WithdrawRequest


class AgentLevelSerializer(serializers.ModelSerializer):
    """代理商等级序列化器"""

    class Meta:
        model = AgentLevel
        fields = [
            'id', 'name', 'commission_rate', 'fee', 'service_content',
            'sort_order', 'is_active', 'description', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class UserBasicSerializer(serializers.ModelSerializer):
    """用户基本信息序列化器"""
    
    class Meta:
        model = User
        fields = ['id', 'email', 'points']


class UserAgentSerializer(serializers.ModelSerializer):
    """用户代理关系序列化器"""
    user = UserBasicSerializer(read_only=True)
    agent_level = AgentLevelSerializer(read_only=True)
    user_id = serializers.IntegerField(write_only=True, required=False)
    user_email = serializers.EmailField(write_only=True, required=False)
    agent_level_id = serializers.IntegerField()

    class Meta:
        model = UserAgent
        fields = [
            'id', 'user_id', 'user_email', 'agent_level_id', 'agent_mode',
            'custom_commission_rate', 'total_credit', 'remaining_credit',
            'domain', 'is_active', 'created_at', 'updated_at',
            'user', 'agent_level'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_user_id(self, value):
        """验证用户ID"""
        try:
            user = User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("用户不存在")
        
        # 检查用户是否已经是代理商
        if hasattr(user, 'agent_profile') and self.instance is None:
            raise serializers.ValidationError("该用户已经是代理商")
        
        return value
    
    def validate_agent_level_id(self, value):
        """验证代理等级ID"""
        try:
            agent_level = AgentLevel.objects.get(id=value, is_active=True)
        except AgentLevel.DoesNotExist:
            raise serializers.ValidationError("代理等级不存在或已禁用")
        
        return value
    
    def validate_user_email(self, value):
        """验证用户邮箱"""
        if value:
            try:
                user = User.objects.get(email=value)
            except User.DoesNotExist:
                raise serializers.ValidationError("该邮箱对应的用户不存在")

            # 检查用户是否已经是代理商
            if hasattr(user, 'agent_profile') and self.instance is None:
                raise serializers.ValidationError("该用户已经是代理商")

        return value

    def validate_domain(self, value):
        """验证域名"""
        if value:
            # 检查域名是否已被使用
            existing = UserAgent.objects.filter(domain=value)
            if self.instance:
                existing = existing.exclude(id=self.instance.id)

            if existing.exists():
                raise serializers.ValidationError("该域名已被绑定")

        return value

    def validate(self, attrs):
        """整体验证"""
        # 创建时必须提供user_id或user_email之一
        if self.instance is None:  # 创建操作
            if not attrs.get('user_id') and not attrs.get('user_email'):
                raise serializers.ValidationError("必须提供用户ID或用户邮箱")

        return attrs
    
    def create(self, validated_data):
        """创建用户代理关系"""
        # 移除不需要保存到模型的字段
        user_id = validated_data.pop('user_id', None)
        user_email = validated_data.pop('user_email', None)
        agent_level_id = validated_data.pop('agent_level_id')

        # 根据提供的信息获取用户
        if user_email:
            user = User.objects.get(email=user_email)
        elif user_id:
            user = User.objects.get(id=user_id)
        else:
            raise serializers.ValidationError("必须提供用户ID或用户邮箱")

        agent_level = AgentLevel.objects.get(id=agent_level_id)

        return UserAgent.objects.create(
            user=user,
            agent_level=agent_level,
            **validated_data
        )
    
    def update(self, instance, validated_data):
        """更新用户代理关系"""
        # 移除不允许修改的字段
        if 'user_id' in validated_data:
            validated_data.pop('user_id')  # 不允许修改用户
        if 'user_email' in validated_data:
            validated_data.pop('user_email')  # 不允许修改用户邮箱

        if 'agent_level_id' in validated_data:
            agent_level_id = validated_data.pop('agent_level_id')
            instance.agent_level = AgentLevel.objects.get(id=agent_level_id)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance


class CommissionRecordSerializer(serializers.ModelSerializer):
    """分成记录序列化器"""
    agent_user = UserBasicSerializer(read_only=True)
    consumer_user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = CommissionRecord
        fields = [
            'id', 'agent_user', 'consumer_user', 'order_id',
            'order_amount', 'commission_rate', 'commission_amount',
            'status', 'created_at', 'settled_at'
        ]


class CommissionStatsSerializer(serializers.Serializer):
    """分成统计序列化器"""
    total_commission = serializers.DecimalField(max_digits=10, decimal_places=2)
    this_month_commission = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_orders = serializers.IntegerField()
    active_users = serializers.IntegerField()
    commission_records = serializers.ListField(
        child=serializers.DictField()
    )


class RechargeRecordSerializer(serializers.ModelSerializer):
    """充值记录序列化器"""
    agent_user = UserBasicSerializer(read_only=True)

    class Meta:
        model = RechargeRecord
        fields = [
            'id', 'agent_user', 'recharge_amount', 'credit_amount',
            'commission_rate', 'status', 'payment_code',
            'created_at', 'paid_at'
        ]
        read_only_fields = ['id', 'created_at', 'paid_at']


class BillRecordSerializer(serializers.ModelSerializer):
    """账单记录序列化器"""
    agent_user = UserBasicSerializer(read_only=True)
    consumer_user = UserBasicSerializer(read_only=True)

    class Meta:
        model = BillRecord
        fields = [
            'id', 'agent_user', 'consumer_user', 'order_id',
            'order_amount', 'headquarters_commission', 'remaining_credit', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class RechargeRequestSerializer(serializers.Serializer):
    """充值请求序列化器"""
    recharge_amount = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=1000,
        error_messages={
            'min_value': '充值金额最少为1000元'
        }
    )


class DomainCheckSerializer(serializers.Serializer):
    """域名检查序列化器"""
    domain = serializers.CharField(max_length=255)
    available = serializers.BooleanField()


class AgentInfoSerializer(serializers.Serializer):
    """代理商信息序列化器"""
    agent_user_id = serializers.IntegerField()
    agent_level = AgentLevelSerializer()
    custom_commission_rate = serializers.DecimalField(
        max_digits=5,
        decimal_places=2,
        required=False,
        allow_null=True
    )


class AgentPaymentInfoSerializer(serializers.ModelSerializer):
    """代理商收款信息序列化器"""
    has_payment_method = serializers.ReadOnlyField()
    available_payment_methods = serializers.ReadOnlyField()

    class Meta:
        model = AgentPaymentInfo
        fields = [
            'id', 'bank_name', 'bank_account', 'account_holder',
            'wechat_qr_code', 'alipay_qr_code', 'contact_phone',
            'has_payment_method', 'available_payment_methods',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate(self, data):
        """验证至少配置一种收款方式"""
        bank_complete = all([
            data.get('bank_name'),
            data.get('bank_account'),
            data.get('account_holder')
        ])

        has_method = (
            bank_complete or
            data.get('wechat_qr_code') or
            data.get('alipay_qr_code')
        )

        if not has_method:
            raise serializers.ValidationError(
                '请至少配置一种收款方式：银行卡（需完整信息）、微信收款码或支付宝收款码'
            )

        return data


class WithdrawRequestSerializer(serializers.ModelSerializer):
    """提现申请序列化器"""
    agent_user = UserBasicSerializer(read_only=True)
    reviewed_by = UserBasicSerializer(read_only=True)
    completed_by = UserBasicSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = WithdrawRequest
        fields = [
            'id', 'agent_user', 'withdraw_amount', 'fee_rate', 'fee_amount',
            'actual_amount', 'status', 'status_display', 'reviewed_by',
            'reviewed_at', 'reject_reason', 'completed_by', 'completed_at',
            'payment_reference', 'payment_method', 'payment_info_snapshot',
            'created_at'
        ]
        read_only_fields = [
            'id', 'agent_user', 'fee_amount', 'actual_amount', 'reviewed_by',
            'reviewed_at', 'completed_by', 'completed_at', 'created_at'
        ]


class WithdrawRequestCreateSerializer(serializers.Serializer):
    """提现申请创建序列化器"""
    withdraw_amount = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0.01,
        error_messages={
            'min_value': '提现金额必须大于0'
        }
    )

    payment_method = serializers.ChoiceField(
        choices=['bank', 'wechat', 'alipay'],
        error_messages={
            'invalid_choice': '收款方式必须是：bank（银行卡）、wechat（微信）或alipay（支付宝）'
        }
    )

    def validate_withdraw_amount(self, value):
        """验证提现金额"""
        # 这里会在视图中进行更详细的验证（最低金额、余额检查等）
        return value


class WithdrawRequestReviewSerializer(serializers.Serializer):
    """提现申请审核序列化器"""
    action = serializers.ChoiceField(
        choices=['approve', 'reject'],
        error_messages={
            'invalid_choice': '操作必须是：approve（通过）或reject（拒绝）'
        }
    )

    reject_reason = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=500,
        help_text='拒绝时的原因说明'
    )

    def validate(self, data):
        """验证审核数据"""
        if data['action'] == 'reject' and not data.get('reject_reason'):
            # 拒绝时原因不是必填，但建议填写
            pass
        return data


class WithdrawRequestCompleteSerializer(serializers.Serializer):
    """提现申请完成（打款）序列化器"""
    payment_reference = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        help_text='银行转账凭证号或其他支付凭证'
    )
