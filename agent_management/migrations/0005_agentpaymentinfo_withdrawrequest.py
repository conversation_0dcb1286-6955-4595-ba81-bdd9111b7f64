# Generated by Django 5.2.4 on 2025-07-18 07:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agent_management', '0004_billrecord_headquarters_commission'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AgentPaymentInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bank_name', models.CharField(blank=True, help_text='开户银行名称', max_length=100, verbose_name='银行名称')),
                ('bank_account', models.CharField(blank=True, help_text='银行卡账号', max_length=50, verbose_name='银行卡号')),
                ('account_holder', models.CharField(blank=True, help_text='银行卡开户人姓名', max_length=50, verbose_name='开户人姓名')),
                ('wechat_qr_code', models.TextField(blank=True, help_text='微信收款二维码图片的Base64编码或URL', verbose_name='微信收款码')),
                ('alipay_qr_code', models.TextField(blank=True, help_text='支付宝收款二维码图片的Base64编码或URL', verbose_name='支付宝收款码')),
                ('contact_phone', models.CharField(blank=True, help_text='收款人联系电话', max_length=20, verbose_name='联系电话')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('agent_user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payment_info', to=settings.AUTH_USER_MODEL, verbose_name='代理商用户')),
            ],
            options={
                'verbose_name': '代理商收款信息',
                'verbose_name_plural': '代理商收款信息',
                'db_table': 'agent_payment_info',
            },
        ),
        migrations.CreateModel(
            name='WithdrawRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('withdraw_amount', models.DecimalField(decimal_places=2, help_text='用户申请的提现金额', max_digits=10, verbose_name='提现金额')),
                ('fee_rate', models.DecimalField(decimal_places=2, help_text='申请时的手续费比例（百分比）', max_digits=5, verbose_name='手续费比例')),
                ('fee_amount', models.DecimalField(decimal_places=2, help_text='根据手续费比例计算的手续费金额', max_digits=10, verbose_name='手续费金额')),
                ('actual_amount', models.DecimalField(decimal_places=2, help_text='扣除手续费后的实际到账金额', max_digits=10, verbose_name='实际到账金额')),
                ('status', models.CharField(choices=[('pending', '待审核'), ('approved', '已通过'), ('rejected', '已拒绝'), ('completed', '已打款')], default='pending', max_length=20, verbose_name='状态')),
                ('reviewed_at', models.DateTimeField(blank=True, null=True, verbose_name='审核时间')),
                ('reject_reason', models.TextField(blank=True, help_text='审核拒绝时的原因说明', verbose_name='拒绝原因')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='打款时间')),
                ('payment_reference', models.CharField(blank=True, help_text='银行转账凭证号或其他支付凭证', max_length=100, verbose_name='打款凭证')),
                ('payment_method', models.CharField(help_text='bank/wechat/alipay', max_length=20, verbose_name='收款方式')),
                ('payment_info_snapshot', models.JSONField(help_text='申请时的收款信息快照，防止后续修改影响审核', verbose_name='收款信息快照')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='申请时间')),
                ('agent_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdraw_requests', to=settings.AUTH_USER_MODEL, verbose_name='代理商用户')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_withdraws', to=settings.AUTH_USER_MODEL, verbose_name='打款操作人')),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_withdraws', to=settings.AUTH_USER_MODEL, verbose_name='审核人')),
            ],
            options={
                'verbose_name': '提现申请',
                'verbose_name_plural': '提现申请',
                'db_table': 'withdraw_request',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['agent_user', 'status'], name='withdraw_re_agent_u_e69d0b_idx'), models.Index(fields=['status', 'created_at'], name='withdraw_re_status_046dde_idx'), models.Index(fields=['reviewed_by'], name='withdraw_re_reviewe_dc34a1_idx')],
                'constraints': [models.UniqueConstraint(condition=models.Q(('status', 'pending')), fields=('agent_user',), name='unique_pending_withdraw_per_agent')],
            },
        ),
    ]
