#!/usr/bin/env python3
"""
提现功能API测试脚本
"""

import os
import sys
import django
from decimal import Decimal

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'writepro_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from agent_management.models import AgentLevel, UserAgent, CommissionRecord, AgentPaymentInfo, WithdrawRequest
from system_settings.models import SystemSettings

User = get_user_model()

def test_withdraw_functionality():
    """测试提现功能"""
    print("🧪 开始测试提现功能...")
    
    try:
        # 1. 检查系统设置
        print("\n1. 检查系统设置...")
        settings = SystemSettings.get_settings()
        withdraw_settings = settings.get_withdraw_settings()
        print(f"   最低提现金额: ¥{withdraw_settings['min_withdraw_amount']}")
        print(f"   提现手续费比例: {withdraw_settings['withdraw_fee_rate']}%")
        
        # 2. 创建测试数据
        print("\n2. 创建测试数据...")
        
        # 创建代理等级
        agent_level, created = AgentLevel.objects.get_or_create(
            name='测试代理',
            defaults={
                'commission_rate': Decimal('18.00'),
                'fee': Decimal('0.00'),
                'description': '测试用代理等级'
            }
        )
        print(f"   代理等级: {agent_level.name} (分成比例: {agent_level.commission_rate}%)")
        
        # 创建测试用户
        test_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={'is_active': True}
        )
        if created:
            test_user.set_password('testpass123')
            test_user.save()
        print(f"   测试用户: {test_user.email}")
        
        # 创建代理关系
        user_agent, created = UserAgent.objects.get_or_create(
            user=test_user,
            defaults={
                'agent_level': agent_level,
                'agent_mode': 'commission',
                'is_active': True
            }
        )
        print(f"   代理关系: {user_agent.agent_mode}模式")
        
        # 创建分成记录（模拟收入）
        commission_records = []
        for i in range(3):
            record, created = CommissionRecord.objects.get_or_create(
                agent_user=test_user,
                consumer_user=test_user,  # 简化测试，使用同一用户
                order_id=f'TEST_ORDER_{i+1}',
                defaults={
                    'order_amount': Decimal('100.00'),
                    'commission_rate': agent_level.commission_rate,
                    'commission_amount': Decimal('18.00'),
                    'status': 'settled'
                }
            )
            if created:
                commission_records.append(record)
        
        total_commission = sum(r.commission_amount for r in commission_records)
        print(f"   创建分成记录: {len(commission_records)}条，总分成: ¥{total_commission}")
        
        # 3. 测试收款信息
        print("\n3. 测试收款信息...")
        payment_info, created = AgentPaymentInfo.objects.get_or_create(
            agent_user=test_user,
            defaults={
                'bank_name': '中国银行',
                'bank_account': '6217000000000001234',
                'account_holder': '测试用户',
                'contact_phone': '***********'
            }
        )
        print(f"   收款信息: {payment_info.bank_name} - {payment_info.account_holder}")
        print(f"   可用收款方式: {payment_info.available_payment_methods}")
        print(f"   是否配置收款方式: {payment_info.has_payment_method}")
        
        # 4. 测试余额计算
        print("\n4. 测试余额计算...")
        from agent_management.views import calculate_agent_balance
        available_balance = calculate_agent_balance(test_user)
        print(f"   可用余额: ¥{available_balance}")
        
        # 5. 测试提现申请
        print("\n5. 测试提现申请...")
        
        # 检查是否已有待审核申请
        existing_request = WithdrawRequest.objects.filter(
            agent_user=test_user,
            status='pending'
        ).first()
        
        if existing_request:
            print(f"   已存在待审核申请: ID {existing_request.id}")
        else:
            # 创建提现申请
            withdraw_amount = Decimal('500.00')
            fee_rate = withdraw_settings['withdraw_fee_rate']
            
            if withdraw_amount <= available_balance and withdraw_amount >= withdraw_settings['min_withdraw_amount']:
                withdraw_request = WithdrawRequest.objects.create(
                    agent_user=test_user,
                    withdraw_amount=withdraw_amount,
                    fee_rate=fee_rate,
                    payment_method='bank',
                    payment_info_snapshot={
                        'bank_name': payment_info.bank_name,
                        'bank_account': payment_info.bank_account,
                        'account_holder': payment_info.account_holder,
                        'contact_phone': payment_info.contact_phone
                    }
                )
                print(f"   创建提现申请: ID {withdraw_request.id}")
                print(f"   提现金额: ¥{withdraw_request.withdraw_amount}")
                print(f"   手续费: ¥{withdraw_request.fee_amount} ({withdraw_request.fee_rate}%)")
                print(f"   实际到账: ¥{withdraw_request.actual_amount}")
                print(f"   状态: {withdraw_request.get_status_display()}")
            else:
                print(f"   ❌ 提现金额验证失败: 金额¥{withdraw_amount}, 余额¥{available_balance}, 最低金额¥{withdraw_settings['min_withdraw_amount']}")
        
        # 6. 测试数据库约束
        print("\n6. 测试数据库约束...")
        try:
            # 尝试创建第二个待审核申请（应该失败）
            duplicate_request = WithdrawRequest(
                agent_user=test_user,
                withdraw_amount=Decimal('100.00'),
                fee_rate=fee_rate,
                payment_method='bank',
                payment_info_snapshot={}
            )
            duplicate_request.save()
            print("   ❌ 数据库约束失效：允许创建重复的待审核申请")
        except Exception as e:
            print(f"   ✅ 数据库约束正常：{str(e)[:50]}...")
        
        # 7. 测试提现记录查询
        print("\n7. 测试提现记录查询...")
        all_requests = WithdrawRequest.objects.filter(agent_user=test_user)
        print(f"   该用户的提现记录数量: {all_requests.count()}")
        
        for request in all_requests:
            print(f"   - ID: {request.id}, 金额: ¥{request.withdraw_amount}, 状态: {request.get_status_display()}")
        
        print("\n✅ 提现功能测试完成！")
        
        # 8. 清理测试数据（可选）
        cleanup = input("\n是否清理测试数据？(y/N): ").lower().strip()
        if cleanup == 'y':
            print("\n🧹 清理测试数据...")
            WithdrawRequest.objects.filter(agent_user=test_user).delete()
            AgentPaymentInfo.objects.filter(agent_user=test_user).delete()
            CommissionRecord.objects.filter(agent_user=test_user).delete()
            UserAgent.objects.filter(user=test_user).delete()
            test_user.delete()
            agent_level.delete()
            print("   测试数据已清理")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_withdraw_functionality()
