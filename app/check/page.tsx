"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { createCheckOrder } from "@/lib/check-api"
import { getUserInfo, type User as UserType } from "@/lib/auth-api"
import {
  Upload,
  FileText,
  Shield,
  Zap,
  Lock,
  Target,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Clock,
  Eye,
  Lightbulb,
  Sparkles,
  History,
} from "lucide-react"

export default function CheckPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [content, setContent] = useState("")
  const [activeTab, setActiveTab] = useState("upload")
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [showResults, setShowResults] = useState(false)
  const [results, setResults] = useState<any>(null)
  const [user, setUser] = useState<UserType | null>(null)
  const [mounted, setMounted] = useState(false)
  const [showSubmitDialog, setShowSubmitDialog] = useState(false)
  const [currentOrderId, setCurrentOrderId] = useState<string | null>(null)
  const { toast } = useToast()
  const router = useRouter()

  // 组件挂载后获取用户信息
  useEffect(() => {
    setMounted(true)
    loadUserInfo()
  }, [])

  // 监听登录状态变化
  useEffect(() => {
    // 监听localStorage变化，当用户登录/退出时更新状态
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'access_token') {
        console.log('🔄 [查AIGC页面] 检测到token变化，重新加载用户信息')
        loadUserInfo()
      }
    }

    // 监听自定义事件（用于同一页面内的状态变化）
    const handleAuthChange = () => {
      console.log('🔄 [查AIGC页面] 检测到登录状态变化，重新加载用户信息')
      loadUserInfo()
    }

    window.addEventListener('storage', handleStorageChange)
    window.addEventListener('authStateChanged', handleAuthChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('authStateChanged', handleAuthChange)
    }
  }, [])

  const loadUserInfo = async () => {
    try {
      // 先检查是否有token
      const token = localStorage.getItem('access_token')
      if (!token) {
        console.log('🔍 [查AIGC页面] 没有找到token，清除用户状态')
        setUser(null)
        return
      }

      const userResponse = await getUserInfo()
      if (userResponse.success && userResponse.data) {
        console.log('✅ [查AIGC页面] 用户信息加载成功:', userResponse.data.user.email)
        setUser(userResponse.data.user)
      } else {
        console.log('❌ [查AIGC页面] 获取用户信息失败，清除用户状态')
        setUser(null)
      }
    } catch (error) {
      console.error('💥 [查AIGC页面] 获取用户信息异常:', error)
      setUser(null)
    }
  }

  // 处理订单提交成功后的操作
  const handleViewOrder = () => {
    setShowSubmitDialog(false)
    router.push('/history')  // 统一跳转到订单列表页
  }

  const handleCloseDialog = () => {
    setShowSubmitDialog(false)
    setCurrentOrderId(null)
    // 清空表单
    setSelectedFile(null)
    setContent("")
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "文件过大",
          description: "请上传小于10MB的文件",
          variant: "destructive",
        })
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        const text = e.target?.result as string
        setContent(text)
        // 移除文件上传成功的toast提示
        // toast({
        //   title: "文件上传成功",
        //   description: `已读取 ${text.length} 个字符`,
        // })
      }
      reader.readAsText(file)
      setSelectedFile(file)
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file) {
      setSelectedFile(file)
      // 触发文件读取
      const input = document.createElement("input")
      input.type = "file"
      const dt = new DataTransfer()
      dt.items.add(file)
      input.files = dt.files
      handleFileUpload({ target: input } as any)
    }
  }

  const getContentLength = () => {
    if (activeTab === "upload" && content) {
      return content.length
    } else if (activeTab === "paste" && content) {
      return content.length
    }
    return 0
  }

  const handleAnalyze = async () => {
    console.log('🔍 [查AIGC页面] 开始检测分析')

    // 检查登录状态
    if (!user) {
      toast({
        title: "请先登录",
        description: "登录后即可使用免费检测服务",
        variant: "destructive",
      })
      // 这里应该触发登录弹窗，而不是跳转到不存在的登录页面
      // 暂时先返回，用户可以点击右上角的登录按钮
      return
    }

    const contentLength = getContentLength()

    if (contentLength === 0) {
      toast({
        title: "请输入内容",
        description: "请上传文件或粘贴文本内容",
        variant: "destructive",
      })
      return
    }

    setIsAnalyzing(true)

    try {
      // 准备请求数据
      const request = activeTab === "upload" && selectedFile
        ? { file: selectedFile }
        : { content }

      console.log('📤 [查AIGC页面] 提交检测请求:', {
        hasFile: !!selectedFile,
        contentLength,
        activeTab
      })

      // 调用API创建订单
      const response = await createCheckOrder(request)

      if (response.success && response.data) {
        console.log('✅ [查AIGC页面] 订单创建成功:', response.data.orderId)

        setCurrentOrderId(response.data.orderId)
        setIsAnalyzing(false)
        setShowSubmitDialog(true)
      } else {
        console.error('❌ [查AIGC页面] 订单创建失败:', response.message)
        setIsAnalyzing(false)
        toast({
          title: "提交失败",
          description: response.message || "提交检测请求失败，请重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('💥 [查AIGC页面] 提交异常:', error)
      setIsAnalyzing(false)
      toast({
        title: "提交失败",
        description: "网络错误，请检查网络连接后重试",
        variant: "destructive",
      })
    }
  }

  const getRiskColor = (score: number) => {
    if (score < 30) return "text-green-600"
    if (score < 60) return "text-yellow-600"
    return "text-red-600"
  }

  const getRiskIcon = (score: number) => {
    if (score < 30) return <CheckCircle className="h-5 w-5 text-green-600" />
    if (score < 60) return <AlertTriangle className="h-5 w-5 text-yellow-600" />
    return <XCircle className="h-5 w-5 text-red-600" />
  }

  const getRiskLevel = (score: number) => {
    if (score < 30) return "低风险"
    if (score < 60) return "中风险"
    return "高风险"
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-orange-200 rounded-full px-6 py-3 mb-6">
            <Sparkles className="h-5 w-5 text-orange-500" />
            <span className="text-sm font-medium text-gray-700">免费AIGC内容检测服务</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            智能检测
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-red-500">AIGC内容</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            上传文档或粘贴文本，30秒内获得专业的AI生成内容检测报告
          </p>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-[1fr,400px] gap-6 items-start">
            {/* Left Column - Main Detection Area */}
            <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
              <CardContent className="p-6">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-2 mb-4 bg-orange-50 p-1">
                    <TabsTrigger
                      value="upload"
                      className="data-[state=active]:bg-white data-[state=active]:text-orange-600 data-[state=active]:shadow-sm font-medium"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      上传文档
                    </TabsTrigger>
                    <TabsTrigger
                      value="paste"
                      className="data-[state=active]:bg-white data-[state=active]:text-orange-600 data-[state=active]:shadow-sm font-medium"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      粘贴文本
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="upload" className="space-y-4">
                    <div
                      className="border-2 border-dashed border-orange-300 rounded-xl p-8 text-center hover:border-orange-400 hover:bg-orange-50/50 transition-all duration-300 cursor-pointer group"
                      onDragOver={handleDragOver}
                      onDrop={handleDrop}
                      onClick={() => {
                        const fileInput = document.getElementById("file-upload") as HTMLInputElement
                        if (fileInput) {
                          fileInput.click()
                        }
                      }}
                    >
                      {selectedFile ? (
                        <div className="flex items-center justify-center gap-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                            <FileText className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-left">
                            <p className="font-bold text-lg text-gray-800">{selectedFile.name}</p>
                            <p className="text-gray-500 text-sm">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                            <Badge className="bg-green-100 text-green-700 border-green-200 mt-1">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              上传成功
                            </Badge>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                            <Upload className="h-8 w-8 text-orange-500" />
                          </div>
                          <p className="text-lg font-bold text-gray-800 mb-2">拖拽文档到此处或点击上传</p>
                          <p className="text-gray-500 mb-4 text-sm">支持 PDF、Word、TXT、PPT 等格式，最大 10MB</p>
                          <div className="flex justify-center items-center space-x-6 text-xs text-gray-500">
                            <div className="flex items-center space-x-1">
                              <Shield className="h-3 w-3 text-orange-500" />
                              <span>安全加密</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Zap className="h-3 w-3 text-orange-500" />
                              <span>快速检测</span>
                            </div>
                          </div>
                        </div>
                      )}
                      <input
                        id="file-upload"
                        type="file"
                        className="hidden"
                        accept=".pdf,.doc,.docx,.txt,.ppt,.pptx"
                        onChange={handleFileUpload}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="paste" className="space-y-4">
                    <div className="space-y-2">
                      <Textarea
                        placeholder="请粘贴您需要检测的文本内容..."
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        className="min-h-[240px] resize-none border-orange-200 focus:border-orange-400 focus:ring-orange-400"
                      />
                      <div className="flex justify-between items-center text-xs text-gray-500">
                        <span>字符数: {getContentLength()}</span>
                        <span>建议字符数: 100-5000</span>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                {/* Progress Bar */}
                {isAnalyzing && (
                  <div className="space-y-2 mt-4">
                    <Progress value={progress} className="h-2" />
                    <p className="text-sm text-center text-gray-600">检测进度: {progress}%</p>
                  </div>
                )}

                {/* Action Button */}
                <div className="mt-6">
                  <Button
                    onClick={handleAnalyze}
                    disabled={isAnalyzing || getContentLength() === 0}
                    className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold py-3"
                    size="lg"
                  >
                    {isAnalyzing ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>正在分析中...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Shield className="h-4 w-4" />
                        <span>开始免费检测</span>
                      </div>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Right Column - Features */}
            <div className="space-y-6">
              {/* Service Features */}
              <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-lg">服务特色</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">完全免费</h4>
                      <p className="text-gray-600 text-sm">无需付费，永久免费使用AIGC检测服务</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Zap className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">快速检测</h4>
                      <p className="text-gray-600 text-sm">30秒内完成分析，快速获得检测结果</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Lock className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">隐私保护</h4>
                      <p className="text-gray-600 text-sm">检测后立即删除内容，保护您的隐私安全</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Target className="h-4 w-4 text-orange-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">精准分析</h4>
                      <p className="text-gray-600 text-sm">95%准确率，提供详细的分析报告和建议</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Usage Statistics */}
              <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-lg">使用统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">今日检测次数</span>
                    <span className="font-bold text-orange-600">1,234</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">累计检测次数</span>
                    <span className="font-bold text-orange-600">156,789</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">平均检测时间</span>
                    <span className="font-bold text-orange-600">2.1秒</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">检测准确率</span>
                    <span className="font-bold text-orange-600">95.2%</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-orange-200/50">
            <div className="text-3xl font-bold text-gray-900 mb-2">156万+</div>
            <div className="text-gray-600">检测次数</div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-orange-200/50">
            <div className="text-3xl font-bold text-gray-900 mb-2">95.2%</div>
            <div className="text-gray-600">准确率</div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-orange-200/50">
            <div className="text-3xl font-bold text-gray-900 mb-2">30秒</div>
            <div className="text-gray-600">检测时长</div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-orange-200/50">
            <div className="text-3xl font-bold text-gray-900 mb-2">完全免费</div>
            <div className="text-gray-600">使用成本</div>
          </div>
        </div>

        {/* 检测结果弹窗保持不变 */}
        <Dialog open={showResults} onOpenChange={setShowResults}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-orange-600" />
                <span>AIGC检测报告</span>
              </DialogTitle>
              <DialogDescription>详细的AI生成内容检测分析结果</DialogDescription>
            </DialogHeader>

            {results && (
              <div className="space-y-6">
                {/* 总体评分 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>总体检测结果</span>
                      <Badge
                        variant={
                          results.overallScore < 30
                            ? "default"
                            : results.overallScore < 60
                              ? "secondary"
                              : "destructive"
                        }
                      >
                        {getRiskLevel(results.overallScore)}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-4 mb-4">
                      {getRiskIcon(results.overallScore)}
                      <div className="flex-1">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">AIGC检测率</span>
                          <span className={`text-2xl font-bold ${getRiskColor(results.overallScore)}`}>
                            {results.overallScore}%
                          </span>
                        </div>
                        <Progress value={results.overallScore} className="h-3" />
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1 text-gray-600">
                          <Clock className="h-4 w-4" />
                          <span>处理时间</span>
                        </div>
                        <div className="font-medium">{results.processingTime}</div>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1 text-gray-600">
                          <FileText className="h-4 w-4" />
                          <span>字符数量</span>
                        </div>
                        <div className="font-medium">{results.wordCount.toLocaleString()}</div>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1 text-gray-600">
                          <Eye className="h-4 w-4" />
                          <span>分析段落</span>
                        </div>
                        <div className="font-medium">{results.segments.length}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 分段分析 */}
                <Card>
                  <CardHeader>
                    <CardTitle>分段详细分析</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {results.segments.map((segment: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-2">
                            <span className="text-sm font-medium text-gray-700">段落 {index + 1}</span>
                            <div className="flex items-center space-x-2">
                              {getRiskIcon(segment.score)}
                              <span className={`font-bold ${getRiskColor(segment.score)}`}>{segment.score}%</span>
                            </div>
                          </div>
                          <p className="text-sm text-gray-600 mb-3 bg-gray-50 p-3 rounded">{segment.text}...</p>
                          <div className="space-y-1">
                            <span className="text-xs font-medium text-gray-700">检测到的问题：</span>
                            {segment.issues.map((issue: string, issueIndex: number) => (
                              <div key={issueIndex} className="flex items-center space-x-2 text-xs text-gray-600">
                                <div className="w-1 h-1 bg-orange-400 rounded-full"></div>
                                <span>{issue}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* 优化建议 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Lightbulb className="h-5 w-5 text-yellow-500" />
                      <span>优化建议</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {results.suggestions.map((suggestion: string, index: number) => (
                        <div key={index} className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <span className="text-xs font-bold text-orange-600">{index + 1}</span>
                          </div>
                          <p className="text-sm text-gray-700">{suggestion}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* 操作按钮 */}
                <div className="flex justify-center space-x-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowResults(false)}
                    className="border-orange-200 hover:bg-orange-50"
                  >
                    继续检测
                  </Button>
                  <Button
                    onClick={() => {
                      // 这里可以添加导出报告的逻辑
                      toast({
                        title: "报告已保存",
                        description: "检测报告已保存到您的历史记录中",
                      })
                    }}
                    className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                  >
                    保存报告
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* 订单提交成功弹窗 */}
        <Dialog open={showSubmitDialog} onOpenChange={setShowSubmitDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span>订单已提交</span>
              </DialogTitle>
              <DialogDescription>
                您的检测请求已成功提交，系统正在处理中，您可以在订单列表中查看处理进度。
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Shield className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">检测信息</span>
                </div>
                <div className="space-y-1 text-sm text-blue-700">
                  <div>订单ID: {currentOrderId}</div>
                  <div>服务类型: AIGC内容检测</div>
                  <div>处理状态: 正在处理中</div>
                  <div>预计时间: 5-10秒</div>
                </div>
              </div>
            </div>

            <DialogFooter className="flex gap-2">
              <Button variant="outline" onClick={handleCloseDialog}>
                关闭
              </Button>
              <Button onClick={handleViewOrder} className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600">
                <History className="h-4 w-4 mr-2" />
                查看我的订单
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
