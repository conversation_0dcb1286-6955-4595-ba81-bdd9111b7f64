"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Phone,
  Upload,
  Save,
  AlertCircle,
  CheckCircle,
  Image as ImageIcon,
  Settings,
  ArrowLeft,
} from "lucide-react"
import { saveAgentContactSettings, getAgentContactSettings } from "@/lib/partnership-api"

export default function AgentContactSettingsPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    phone: "",
    wechat_qr_code: null as File | null
  })
  const [currentSettings, setCurrentSettings] = useState({
    phone: "",
    wechat_qr_code: ""
  })
  const [loading, setLoading] = useState(false)
  const [dataLoading, setDataLoading] = useState(true)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)

  useEffect(() => {
    loadCurrentSettings()
  }, [])

  const loadCurrentSettings = async () => {
    setDataLoading(true)
    try {
      // 获取当前代理商的联系方式设置
      const response = await getAgentContactSettings()
      if (response.success) {
        setCurrentSettings(response.data)
        setFormData({
          phone: response.data.phone || "",
          wechat_qr_code: null
        })
      } else {
        console.error('获取联系方式配置失败:', response)
      }
    } catch (error) {
      console.error('Failed to load current settings:', error)
    } finally {
      setDataLoading(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        setMessage({ type: 'error', text: '请选择图片文件' })
        return
      }

      // 验证文件大小（限制为5MB）
      if (file.size > 5 * 1024 * 1024) {
        setMessage({ type: 'error', text: '图片文件大小不能超过5MB' })
        return
      }

      setFormData(prev => ({ ...prev, wechat_qr_code: file }))
      
      // 创建预览URL
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
      setMessage(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setMessage(null)

    try {
      const submitData = new FormData()
      
      if (formData.phone) {
        submitData.append('phone', formData.phone)
      }
      
      if (formData.wechat_qr_code) {
        submitData.append('wechat_qr_code', formData.wechat_qr_code)
      }

      const response = await saveAgentContactSettings(submitData)
      
      if (response.success) {
        setMessage({ type: 'success', text: '联系方式保存成功！' })
        loadCurrentSettings() // 重新加载当前设置
        
        // 清除文件选择
        setFormData(prev => ({ ...prev, wechat_qr_code: null }))
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl)
          setPreviewUrl(null)
        }
      } else {
        setMessage({ type: 'error', text: '保存失败，请重试' })
      }
    } catch (error) {
      console.error('Failed to save contact settings:', error)
      setMessage({ type: 'error', text: '保存失败，请重试' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/agent')}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>返回代理后台</span>
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">联系方式配置</h1>
          <p className="text-gray-600 mt-2">配置您的专属联系方式，将在招商加盟页面显示</p>
        </div>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* 配置表单 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                我的联系方式
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* 电话号码 */}
                <div>
                  <Label htmlFor="phone">联系电话</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="请输入您的联系电话"
                    className="mt-1"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    客户可以通过此电话联系您
                  </p>
                </div>

                {/* 微信二维码 */}
                <div>
                  <Label htmlFor="wechat_qr_code">微信二维码</Label>
                  <div className="mt-1">
                    <div className="flex items-center justify-center w-full">
                      <label
                        htmlFor="wechat_qr_code"
                        className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
                      >
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <Upload className="w-8 h-8 mb-2 text-gray-500" />
                          <p className="mb-2 text-sm text-gray-500">
                            <span className="font-semibold">点击上传</span> 您的微信二维码
                          </p>
                          <p className="text-xs text-gray-500">PNG, JPG 或 JPEG (最大 5MB)</p>
                        </div>
                        <input
                          id="wechat_qr_code"
                          type="file"
                          className="hidden"
                          accept="image/*"
                          onChange={handleFileChange}
                        />
                      </label>
                    </div>
                    
                    {/* 文件预览 */}
                    {previewUrl && (
                      <div className="mt-4">
                        <p className="text-sm font-medium text-gray-700 mb-2">预览：</p>
                        <img
                          src={previewUrl}
                          alt="微信二维码预览"
                          className="w-32 h-32 object-cover border border-gray-200 rounded-lg"
                        />
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    客户可以扫码添加您的微信
                  </p>
                </div>

                {/* 提交按钮 */}
                <Button
                  type="submit"
                  className="w-full"
                  disabled={loading}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? '保存中...' : '保存设置'}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* 当前设置预览 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                客户看到的联系方式
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 微信二维码预览 */}
              <div className="text-center">
                <h4 className="font-semibold text-gray-900 mb-3">微信咨询</h4>
                <div className="flex justify-center mb-3">
                  {/* 优先显示新上传的预览，其次显示当前设置 */}
                  {dataLoading ? (
                    <div className="w-32 h-32 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                    </div>
                  ) : previewUrl ? (
                    <img
                      src={previewUrl}
                      alt="新上传的微信二维码预览"
                      className="w-32 h-32 border border-gray-200 rounded-lg"
                    />
                  ) : currentSettings.wechat_qr_code ? (
                    <img
                      src={currentSettings.wechat_qr_code}
                      alt="当前微信二维码"
                      className="w-32 h-32 border border-gray-200 rounded-lg"
                    />
                  ) : (
                    <div className="w-32 h-32 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center">
                      <span className="text-gray-400 text-sm">暂未配置</span>
                    </div>
                  )}
                </div>
                <p className="text-sm text-gray-600">扫码添加微信咨询</p>
              </div>

              {/* 电话预览 */}
              <div className="text-center">
                <h4 className="font-semibold text-gray-900 mb-3">电话咨询</h4>
                <div className="flex items-center justify-center">
                  <Phone className="h-4 w-4 mr-2 text-orange-500" />
                  <span className="text-lg font-medium text-gray-900">
                    {/* 优先显示表单中的电话，其次显示当前设置 */}
                    {formData.phone || currentSettings.phone || "未配置"}
                  </span>
                </div>
                
              </div>

              {/* 说明 */}
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  当客户通过您的专属域名访问招商加盟页面时，将显示您配置的联系方式。如果您没有配置，则显示总部联系方式。
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>

        {/* 消息提示 */}
        {message && (
          <Alert className={`mt-6 ${message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
            {message.type === 'success' ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription className={message.type === 'success' ? 'text-green-700' : 'text-red-700'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  )
}
