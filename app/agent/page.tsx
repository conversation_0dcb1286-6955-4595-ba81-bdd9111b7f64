"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import {
  Users,
  Search,
  Filter,
  Download,
  CheckCircle,
  Clock,
  Building,
  CreditCard,
  Wallet,
  Eye,
  AlertCircle,
  Banknote,
  Loader2,
  RefreshCw,
  <PERSON><PERSON>s,
  Phone,
  FileText,
  ChevronRight,
  ArrowRight,
} from "lucide-react"
import {
  getAgentDashboard,
  getMyCommissionRecords,
  createRechargeOrder,
  confirmRechargePayment,
  getRechargeRecords,
  getBillRecords,
  getPaymentInfo,
  savePaymentInfo,
  getWithdrawSettings,
  createWithdrawRequest,
  getWithdrawRecords,
  type AgentDashboardData,
  type CommissionRecord,
  type PaginatedResponse,
  type RechargeRecord,
  type BillRecord,
  type AgentPaymentInfo,
  type WithdrawRequest,
  type WithdrawSettings
} from "@/lib/agent-admin-api"
import { isMockPaymentEnabled } from "@/lib/system-settings"
import { useRouter } from "next/navigation"

export default function AgentPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const [withdrawAmount, setWithdrawAmount] = useState("")
  const [dashboardData, setDashboardData] = useState<AgentDashboardData | null>(null)
  const [commissionRecords, setCommissionRecords] = useState<PaginatedResponse<CommissionRecord> | null>(null)
  const [loading, setLoading] = useState(true)
  const [commissionLoading, setCommissionLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [billRecords, setBillRecords] = useState<BillRecord[]>([])
  const [rechargeRecords, setRechargeRecords] = useState<RechargeRecord[]>([])
  const [billLoading, setBillLoading] = useState(false)
  const [rechargeLoading, setRechargeLoading] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [paymentCode, setPaymentCode] = useState("")
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [mockPaymentEnabled, setMockPaymentEnabled] = useState(false)

  // 提现相关状态
  const [paymentInfo, setPaymentInfo] = useState<AgentPaymentInfo | null>(null)
  const [withdrawRecords, setWithdrawRecords] = useState<PaginatedResponse<WithdrawRequest> | null>(null)
  const [withdrawSettings, setWithdrawSettings] = useState<WithdrawSettings | null>(null)
  const [withdrawLoading, setWithdrawLoading] = useState(false)
  const [showPaymentInfoDialog, setShowPaymentInfoDialog] = useState(false)
  const [showWithdrawDialog, setShowWithdrawDialog] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'bank' | 'wechat' | 'alipay'>('bank')

  const { toast } = useToast()
  const router = useRouter()

  // 导航函数
  const navigateToContactSettings = () => {
    router.push('/agent/contact-settings')
  }

  const navigateToAgentOrders = () => {
    router.push('/agent/agent-orders')
  }

  // 获取仪表板数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      const data = await getAgentDashboard()
      setDashboardData(data)
      setLastUpdated(new Date()) // 设置更新时间

      // 如果是预付费模式，加载账单和充值记录
      if (data.agent_info.agent_mode === 'prepaid') {
        loadBillRecords()
        loadRechargeRecords()
      }
    } catch (error) {
      toast({
        title: "获取数据失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 加载账单记录
  const loadBillRecords = async () => {
    try {
      setBillLoading(true)
      const response = await getBillRecords({ page: 1, page_size: 10 })
      setBillRecords(response.results)
    } catch (error) {
      console.error('Failed to load bill records:', error)
      toast({
        title: "加载失败",
        description: "无法加载账单记录",
        variant: "destructive",
      })
    } finally {
      setBillLoading(false)
    }
  }

  // 加载充值记录
  const loadRechargeRecords = async () => {
    try {
      setRechargeLoading(true)
      const response = await getRechargeRecords({ page: 1, page_size: 10 })
      setRechargeRecords(response.results)
    } catch (error) {
      console.error('Failed to load recharge records:', error)
      toast({
        title: "加载失败",
        description: "无法加载充值记录",
        variant: "destructive",
      })
    } finally {
      setRechargeLoading(false)
    }
  }

  // 获取分成记录
  const fetchCommissionRecords = async (page = 1) => {
    try {
      setCommissionLoading(true)
      const params = {
        page,
        page_size: 10,
        status: statusFilter === 'all' ? undefined : statusFilter,
      }
      const data = await getMyCommissionRecords(params)
      setCommissionRecords(data)
      setCurrentPage(page)
    } catch (error) {
      toast({
        title: "获取分成记录失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setCommissionLoading(false)
    }
  }

  // 页面加载时获取数据
  useEffect(() => {
    fetchDashboardData()
    fetchCommissionRecords()

    // 获取模拟支付状态
    const loadMockPaymentStatus = async () => {
      try {
        const enabled = await isMockPaymentEnabled()
        setMockPaymentEnabled(enabled)
      } catch (error) {
        console.error('获取模拟支付状态失败:', error)
        setMockPaymentEnabled(false)
      }
    }
    loadMockPaymentStatus()

    // 如果是分成模式，获取提现相关数据
    if (dashboardData?.agent_info.agent_mode === 'commission') {
      fetchWithdrawSettings()
      fetchPaymentInfo()
      fetchWithdrawRecords()
    }
  }, [dashboardData?.agent_info.agent_mode])

  // 状态过滤变化时重新获取数据
  useEffect(() => {
    fetchCommissionRecords(1)
  }, [statusFilter])

  // 处理充值
  const handleRecharge = async () => {
    try {
      const amount = parseFloat(withdrawAmount)
      if (amount < 1000) {
        toast({
          title: "充值金额错误",
          description: "最低充值金额为1000元",
          variant: "destructive",
        })
        return
      }

      const response = await createRechargeOrder({ recharge_amount: amount })
      setPaymentCode(response.payment_code)
      setShowPaymentDialog(true)
    } catch (error) {
      toast({
        title: "创建充值订单失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 处理支付成功
  const handlePaymentSuccess = async () => {
    try {
      if (!paymentCode) return

      const rechargeId = parseInt(paymentCode.split('_')[1])
      await confirmRechargePayment(rechargeId)

      setShowPaymentDialog(false)
      setWithdrawAmount("")
      setPaymentCode("")

      // 重新加载数据
      fetchDashboardData()
      loadRechargeRecords()

      toast({
        title: "充值成功",
        description: "您的账户额度已更新",
      })
    } catch (error) {
      toast({
        title: "确认支付失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 安全地转换数值
  const safeNumber = (value: number | string | undefined | null): number => {
    if (value === undefined || value === null) return 0
    const num = Number(value)
    return isNaN(num) ? 0 : num
  }

  // 格式化金额
  const formatAmount = (value: number | string | undefined | null): string => {
    return safeNumber(value).toFixed(2)
  }

  // 格式化百分比
  const formatPercent = (value: number | string | undefined | null): string => {
    return safeNumber(value).toFixed(2)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "settled":
        return (
          <Badge className="bg-green-100 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            已结算
          </Badge>
        )
      case "pending":
        return (
          <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" />
            待结算
          </Badge>
        )
      case "cancelled":
        return (
          <Badge className="bg-red-100 text-red-700 border-red-200">
            <AlertCircle className="w-3 h-3 mr-1" />
            已取消
          </Badge>
        )
      default:
        return null
    }
  }

  // 获取提现设置
  const fetchWithdrawSettings = async () => {
    try {
      const settings = await getWithdrawSettings()
      setWithdrawSettings(settings)
    } catch (error) {
      console.error('获取提现设置失败:', error)
    }
  }

  // 获取收款信息
  const fetchPaymentInfo = async () => {
    try {
      const info = await getPaymentInfo()
      setPaymentInfo(info)
    } catch (error) {
      console.error('获取收款信息失败:', error)
    }
  }

  // 获取提现记录
  const fetchWithdrawRecords = async (page = 1) => {
    try {
      setWithdrawLoading(true)
      const records = await getWithdrawRecords({ page, page_size: 10 })
      setWithdrawRecords(records)
    } catch (error) {
      toast({
        title: "获取提现记录失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setWithdrawLoading(false)
    }
  }

  // 保存收款信息
  const handleSavePaymentInfo = async (formData: Partial<AgentPaymentInfo>) => {
    try {
      const savedInfo = await savePaymentInfo(formData)
      setPaymentInfo(savedInfo)
      setShowPaymentInfoDialog(false)
      toast({
        title: "收款信息保存成功",
        description: "您现在可以申请提现了",
      })
    } catch (error) {
      toast({
        title: "保存收款信息失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 提现申请
  const handleWithdraw = async () => {
    if (!dashboardData || !withdrawSettings) return

    const amount = Number.parseFloat(withdrawAmount)

    // 验证提现金额
    if (!withdrawAmount || amount < withdrawSettings.min_withdraw_amount) {
      toast({
        title: "提现金额错误",
        description: `提现金额不能少于${withdrawSettings.min_withdraw_amount}元`,
        variant: "destructive",
      })
      return
    }

    if (amount > withdrawSettings.available_balance) {
      toast({
        title: "提现金额错误",
        description: "提现金额不能超过可用余额",
        variant: "destructive",
      })
      return
    }

    // 检查收款信息
    if (!paymentInfo || !paymentInfo.has_payment_method) {
      toast({
        title: "请先配置收款信息",
        description: "提现前需要完善收款信息",
        variant: "destructive",
      })
      setShowPaymentInfoDialog(true)
      return
    }

    // 验证选择的收款方式
    if (!paymentInfo.available_payment_methods.includes(selectedPaymentMethod)) {
      toast({
        title: "收款方式不可用",
        description: "请选择已配置的收款方式",
        variant: "destructive",
      })
      return
    }

    try {
      await createWithdrawRequest({
        withdraw_amount: amount,
        payment_method: selectedPaymentMethod
      })

      toast({
        title: "提现申请已提交",
        description: "请等待管理员审核，预计1-3个工作日到账",
      })

      setWithdrawAmount("")
      setShowWithdrawDialog(false)

      // 刷新数据
      fetchDashboardData()
      fetchWithdrawRecords()

    } catch (error) {
      toast({
        title: "提现申请失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    }
  }

  const handleRefresh = () => {
    fetchDashboardData()
    fetchCommissionRecords(currentPage)

    // 如果是分成模式，也刷新提现相关数据
    if (dashboardData?.agent_info.agent_mode === 'commission') {
      fetchWithdrawSettings()
      fetchPaymentInfo()
      fetchWithdrawRecords()
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-orange-500" />
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 mx-auto mb-4 text-red-500" />
          <p className="text-gray-600 mb-4">获取数据失败</p>
          <Button onClick={handleRefresh}>重试</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50">
      {/* Header */}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Agent Info Card */}
        <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl mb-8">
          <CardContent className="p-6">
            <div className="grid md:grid-cols-4 gap-6">
              <div className="md:col-span-1">
                <div className="text-center flex flex-col justify-center h-full">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Building className="h-8 w-8 text-white" />
                  </div>
                  <h2 className="text-base font-bold text-gray-900 mb-2">{dashboardData.agent_info.user.email}</h2>
                  <div className="px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full border border-green-200">
                    <p className="text-sm font-semibold text-green-700">
                      分润比例: {dashboardData.agent_info.custom_commission_rate || dashboardData.agent_info.agent_level.commission_rate}%
                    </p>
                  </div>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      {dashboardData.agent_info.agent_level.name}
                    </Badge>
                  </div>
                  {dashboardData.agent_info.domain && (
                    <p className="text-xs text-gray-500 mt-1">
                      域名: {dashboardData.agent_info.domain}
                    </p>
                  )}
                </div>
              </div>
              <div className="md:col-span-3">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-gray-600 mb-1">旗下用户</p>
                    <p className="text-2xl font-bold text-blue-600">{safeNumber(dashboardData.stats.total_users)}</p>
                    <p className="text-xs text-gray-500">活跃: {safeNumber(dashboardData.stats.active_users)}</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-sm text-gray-600 mb-1">总营收</p>
                    <p className="text-2xl font-bold text-green-600">¥{safeNumber(dashboardData.stats.total_revenue).toLocaleString()}</p>
                    <p className="text-xs text-gray-500">累计收入</p>
                  </div>
                  {dashboardData.agent_info.agent_mode === 'prepaid' ? (
                    <>
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <p className="text-sm text-gray-600 mb-1">总额度</p>
                        <p className="text-2xl font-bold text-orange-600">¥{safeNumber(dashboardData.agent_info.total_credit).toLocaleString()}</p>
                        <p className="text-xs text-gray-500">累计额度</p>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <p className="text-sm text-gray-600 mb-1">剩余额度</p>
                        <p className="text-2xl font-bold text-purple-600">¥{safeNumber(dashboardData.agent_info.remaining_credit).toLocaleString()}</p>
                        <p className="text-xs text-gray-500">可用额度</p>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <p className="text-sm text-gray-600 mb-1">总分润</p>
                        <p className="text-2xl font-bold text-orange-600">¥{safeNumber(dashboardData.stats.total_commission).toLocaleString()}</p>
                        <p className="text-xs text-gray-500">累计分润</p>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <p className="text-sm text-gray-600 mb-1">余额</p>
                        <p className="text-2xl font-bold text-purple-600">¥{safeNumber(dashboardData.stats.available_balance).toLocaleString()}</p>
                        <p className="text-xs text-gray-500">本月: ¥{safeNumber(dashboardData.stats.this_month_commission).toLocaleString()}</p>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <Card className="bg-white/90 backdrop-blur-sm border-2 border-transparent hover:border-blue-200 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer group transform hover:-translate-y-1" onClick={navigateToContactSettings}>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-50 text-blue-600 rounded-xl flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                    <Settings className="h-6 w-6" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                      联系方式配置
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      配置您的专属联系方式
                    </CardDescription>
                  </div>
                </div>
                <div className="text-blue-400 group-hover:text-blue-600 transition-colors">
                  <ChevronRight className="h-5 w-5" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></div>
                  <span>微信二维码设置</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></div>
                  <span>联系电话配置</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></div>
                  <span>显示效果预览</span>
                </div>
              </div>
              <div className="mt-4 pt-3 border-t border-gray-100">
                <div className="flex items-center text-sm text-blue-600 font-medium group-hover:text-blue-700 transition-colors">
                  <span>点击进入配置</span>
                  <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/90 backdrop-blur-sm border-2 border-transparent hover:border-green-200 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer group transform hover:-translate-y-1" onClick={navigateToAgentOrders}>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-50 text-green-600 rounded-xl flex items-center justify-center group-hover:bg-green-100 transition-colors">
                    <FileText className="h-6 w-6" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-green-600 transition-colors">
                      代理订单管理
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      查看通过您域名的代理申请
                    </CardDescription>
                  </div>
                </div>
                <div className="text-green-400 group-hover:text-green-600 transition-colors">
                  <ChevronRight className="h-5 w-5" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></div>
                  <span>申请订单查看</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></div>
                  <span>状态跟踪管理</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></div>
                  <span>数据统计分析</span>
                </div>
              </div>
              <div className="mt-4 pt-3 border-t border-gray-100">
                <div className="flex items-center text-sm text-green-600 font-medium group-hover:text-green-700 transition-colors">
                  <span>点击进入管理</span>
                  <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="overview">
              {dashboardData.agent_info.agent_mode === 'prepaid' ? '账单' : '数据管理'}
            </TabsTrigger>
            <TabsTrigger value="withdraw">
              {dashboardData.agent_info.agent_mode === 'prepaid' ? '充值管理' : '提现管理'}
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {dashboardData.agent_info.agent_mode === 'prepaid' ? (
              /* 预付费模式 - 账单记录 */
              <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <Users className="h-5 w-5 text-white" />
                    </div>
                    账单记录
                  </CardTitle>
                  <CardDescription>查看您的消费账单记录</CardDescription>
                </CardHeader>
                <CardContent>
                  {billLoading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-gray-500 mt-2">加载中...</p>
                    </div>
                  ) : billRecords.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              订单信息
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              消费用户
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              订单金额
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              总部分润
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              剩余额度
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              创建时间
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {billRecords.map((record) => (
                            <tr key={record.id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">
                                  {record.order_id}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">
                                  {record.consumer_user.email}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">
                                  ¥{safeNumber(record.order_amount).toLocaleString()}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-orange-600 font-medium">
                                  ¥{safeNumber(record.headquarters_commission).toLocaleString()}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">
                                  ¥{safeNumber(record.remaining_credit).toLocaleString()}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500">
                                  {formatDate(record.created_at)}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <p>暂无账单记录</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              /* 分成模式 - 分润记录 */
              <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <Users className="h-5 w-5 text-white" />
                    </div>
                    分润记录
                  </CardTitle>
                  <CardDescription>查看您的分润收益记录</CardDescription>
                </CardHeader>
              <CardContent>
                {/* Filters */}
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 space-y-2 sm:space-y-0">
                  <div className="flex items-center space-x-2">
                    <Input
                      placeholder="搜索订单ID..."
                      className="w-64 border-orange-200 focus:border-orange-400"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button variant="outline" className="border-orange-200 hover:bg-orange-50">
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-[180px] border-orange-200">
                        <SelectValue placeholder="状态筛选" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        <SelectItem value="pending">待结算</SelectItem>
                        <SelectItem value="settled">已结算</SelectItem>
                        <SelectItem value="cancelled">已取消</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      className="border-orange-200 hover:bg-orange-50"
                      onClick={() => fetchCommissionRecords(1)}
                      disabled={commissionLoading}
                    >
                      {commissionLoading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4 mr-2" />
                      )}
                      刷新
                    </Button>
                  </div>
                </div>

                {/* Commission Records Table */}
                <div className="bg-white rounded-lg overflow-hidden border border-gray-200 mb-8">
                  <div className="overflow-x-auto">
                    {commissionLoading ? (
                      <div className="flex items-center justify-center py-12">
                        <Loader2 className="h-6 w-6 animate-spin text-orange-500" />
                        <span className="ml-2 text-gray-600">加载中...</span>
                      </div>
                    ) : commissionRecords && commissionRecords.results.length > 0 ? (
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              订单信息
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              消费用户
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              订单金额
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              分润比例
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              分润金额
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              创建时间
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              状态
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {commissionRecords.results.map((record) => (
                            <tr key={record.id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div>
                                  <p className="text-sm font-medium text-gray-900">{record.order_id}</p>
                                  <p className="text-sm text-gray-500">ID: {record.id}</p>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                用户ID: {record.consumer_user_id}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ¥{formatAmount(record.order_amount)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatPercent(record.commission_rate)}%
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                                ¥{formatAmount(record.commission_amount)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {formatDate(record.created_at)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(record.status)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : (
                      <div className="text-center py-12">
                        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">暂无分润记录</p>
                      </div>
                    )}
                  </div>
                </div>
                {/* Pagination */}
                {commissionRecords && commissionRecords.results.length > 0 && (
                  <div className="flex justify-between items-center mt-6">
                    <div className="text-sm text-gray-500">
                      显示 {((currentPage - 1) * 10) + 1} - {Math.min(currentPage * 10, commissionRecords.count)} 共 {commissionRecords.count} 条记录
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={!commissionRecords.previous || commissionLoading}
                        onClick={() => fetchCommissionRecords(currentPage - 1)}
                      >
                        上一页
                      </Button>
                      <span className="px-3 py-1 bg-orange-500 text-white rounded text-sm">
                        {currentPage}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={!commissionRecords.next || commissionLoading}
                        onClick={() => fetchCommissionRecords(currentPage + 1)}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            )}
          </TabsContent>

          {/* Withdraw Tab */}
          <TabsContent value="withdraw" className="space-y-6">
            {dashboardData.agent_info.agent_mode === 'prepaid' ? (
              /* 预付费模式 - 充值管理 */
              <div className="grid md:grid-cols-3 gap-6">
                {/* Recharge Form */}
                <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
                  <CardHeader>
                    <CardTitle className="text-lg font-bold text-gray-900 flex items-center gap-2">
                      <Wallet className="h-5 w-5 text-blue-500" />
                      充值额度
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <h4 className="font-semibold text-gray-900 mb-2">当前额度</h4>
                        <div className="text-2xl font-bold text-blue-600">
                          ¥{safeNumber(dashboardData.agent_info.remaining_credit).toLocaleString()}
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          总额度: ¥{safeNumber(dashboardData.agent_info.total_credit).toLocaleString()}
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="recharge-amount">充值金额</Label>
                        <Input
                          id="recharge-amount"
                          type="number"
                          placeholder="请输入充值金额"
                          value={withdrawAmount}
                          onChange={(e) => setWithdrawAmount(e.target.value)}
                          min="1000"
                          className="border-blue-200 focus:border-blue-400"
                        />
                        <p className="text-xs text-gray-500 mt-1">最低充值金额1000元</p>
                      </div>

                      <div className="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
                        <div className="flex items-start">
                          <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 mr-2" />
                          <div className="text-xs text-yellow-800">
                            <p className="font-medium mb-1">充值说明：</p>
                            <ul className="space-y-1">
                              <li>• 充值后将根据您的分成比例计算获得的额度</li>
                              <li>• 每笔订单将从额度中扣除相应金额</li>
                              <li>• 额度不足时订单将暂停处理</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <Button
                        className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                        onClick={handleRecharge}
                        disabled={!withdrawAmount || parseFloat(withdrawAmount) < 1000}
                      >
                        立即充值
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Recharge Records */}
                <div className="md:col-span-2">
                  <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
                    <CardHeader>
                      <CardTitle className="text-lg font-bold text-gray-900">充值记录</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {rechargeLoading ? (
                        <div className="text-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                          <p className="text-gray-500 mt-2">加载中...</p>
                        </div>
                      ) : rechargeRecords.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  充值金额
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  获得额度
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  分成比例
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  状态
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  充值时间
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {rechargeRecords.map((record) => (
                                <tr key={record.id} className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">
                                      ¥{safeNumber(record.recharge_amount).toLocaleString()}
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">
                                      ¥{safeNumber(record.credit_amount).toLocaleString()}
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">
                                      {record.commission_rate}%
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <Badge
                                      variant="outline"
                                      className={
                                        record.status === 'paid'
                                          ? "text-green-600 border-green-200 bg-green-50"
                                          : record.status === 'pending'
                                          ? "text-yellow-600 border-yellow-200 bg-yellow-50"
                                          : "text-red-600 border-red-200 bg-red-50"
                                      }
                                    >
                                      {record.status === 'paid' ? '已支付' :
                                       record.status === 'pending' ? '待支付' : '已取消'}
                                    </Badge>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-500">
                                      {record.paid_at ? formatDate(record.paid_at) : formatDate(record.created_at)}
                                    </div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <p>暂无充值记录</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>
            ) : (
              /* 分成模式 - 提现管理 */
              <div className="grid md:grid-cols-3 gap-6">
                {/* Withdraw Form */}
                <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
                  <CardHeader>
                    <CardTitle className="text-lg font-bold text-gray-900 flex items-center gap-2">
                      <Wallet className="h-5 w-5 text-green-500" />
                      申请提现
                    </CardTitle>
                  </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                      <h4 className="font-semibold text-gray-900 mb-2">余额</h4>
                      <div className="text-2xl font-bold text-green-600">
                        ¥{safeNumber(dashboardData.stats.available_balance).toLocaleString()}
                      </div>
                      {safeNumber(dashboardData.stats.pending_withdraw) > 0 && (
                        <p className="text-sm text-gray-600 mt-1">
                          处理中: ¥{safeNumber(dashboardData.stats.pending_withdraw).toLocaleString()}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="withdraw-amount">提现金额</Label>
                      <Input
                        id="withdraw-amount"
                        type="number"
                        placeholder="请输入提现金额"
                        value={withdrawAmount}
                        onChange={(e) => setWithdrawAmount(e.target.value)}
                        min="100"
                        max={safeNumber(dashboardData.stats.available_balance)}
                        className="border-orange-200 focus:border-orange-400"
                      />
                      <p className="text-xs text-gray-500 mt-1">最低提现金额100元</p>
                    </div>

                    <div className="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
                      <div className="flex items-start">
                        <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 mr-2" />
                        <div className="text-xs text-yellow-800">
                          <p className="font-medium mb-1">提现说明：</p>
                          <ul className="space-y-1">
                            <li>• 手续费：2%</li>
                            <li>• 到账时间：1-3个工作日</li>
                            <li>• 提现时间：工作日9:00-18:00</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <Button
                      onClick={() => setShowWithdrawDialog(true)}
                      className="w-full bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600"
                      disabled={!withdrawAmount || Number.parseFloat(withdrawAmount) < 100}
                    >
                      <Banknote className="h-4 w-4 mr-2" />
                      申请提现
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Withdraw Records */}
              <div className="md:col-span-2">
                <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
                  <CardHeader>
                    <CardTitle className="text-lg font-bold text-gray-900 flex items-center gap-2">
                      <CreditCard className="h-5 w-5 text-blue-500" />
                      提现记录
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-12">
                      <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-2">提现记录功能开发中</p>
                      <p className="text-sm text-gray-400">敬请期待</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* 支付弹窗 */}
      {showPaymentDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-bold text-gray-900 mb-4">扫码支付</h3>
            <div className="text-center">
              <div className="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <div className="text-gray-500">
                  <p className="text-sm">支付二维码</p>
                  <p className="text-xs mt-1">{paymentCode}</p>
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                请使用微信或支付宝扫码支付
              </p>
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setShowPaymentDialog(false)}
                  className="flex-1"
                >
                  取消
                </Button>
                {mockPaymentEnabled && (
                  <Button
                    onClick={handlePaymentSuccess}
                    className="flex-1 bg-green-500 hover:bg-green-600"
                  >
                    🧪 模拟支付完成
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
