"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Search,
  RefreshCw,
  Eye,
  Phone,
  Building,
  Calendar,
  DollarSign,
  Users,
  ArrowLeft,
} from "lucide-react"
import { getAgentApplications } from "@/lib/partnership-api"

interface Application {
  id: number
  user_email: string
  agent_level: {
    id: number
    name: string
    fee: string
    commission_rate: string
  }
  name: string
  phone: string
  company: string
  experience: string
  payment_status: string
  audit_status: string
  audit_reason: string
  amount: string
  created_at: string
}

export default function AgentOrdersPage() {
  const router = useRouter()
  const [applications, setApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [paymentStatusFilter, setPaymentStatusFilter] = useState("all")
  const [auditStatusFilter, setAuditStatusFilter] = useState("all")
  const [pagination, setPagination] = useState({
    count: 0,
    next: null,
    previous: null
  })

  useEffect(() => {
    loadApplications()
  }, [searchTerm, paymentStatusFilter, auditStatusFilter])

  const loadApplications = async () => {
    setLoading(true)
    try {
      const params: any = {}
      if (searchTerm) params.search = searchTerm
      if (paymentStatusFilter && paymentStatusFilter !== "all") params.payment_status = paymentStatusFilter
      if (auditStatusFilter && auditStatusFilter !== "all") params.audit_status = auditStatusFilter

      const response = await getAgentApplications(params)

      if (response.success) {
        setApplications(response.results || [])
        setPagination({
          count: response.count || 0,
          next: response.next,
          previous: response.previous
        })
      }
    } catch (error) {
      console.error('Failed to load applications:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string, type: 'payment' | 'audit') => {
    const statusConfig = {
      payment: {
        paid: { label: '已付款', className: 'bg-green-100 text-green-700' },
        unpaid: { label: '未付款', className: 'bg-red-100 text-red-700' },
        refunded: { label: '已退款', className: 'bg-gray-100 text-gray-700' },
        cancelled: { label: '已取消', className: 'bg-gray-100 text-gray-700' }
      },
      audit: {
        pending: { label: '待审核', className: 'bg-yellow-100 text-yellow-700' },
        approved: { label: '已通过', className: 'bg-green-100 text-green-700' },
        rejected: { label: '已拒绝', className: 'bg-red-100 text-red-700' }
      }
    } as const

    const config = statusConfig[type]?.[status as keyof typeof statusConfig[typeof type]]
    return (
      <Badge className={config?.className || 'bg-gray-100 text-gray-700'}>
        {config?.label || status}
      </Badge>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/agent')}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>返回代理后台</span>
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">代理订单管理</h1>
          <p className="text-gray-600 mt-2">查看通过您的域名提交的代理申请订单</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">总申请数</p>
                  <p className="text-2xl font-bold text-gray-900">{pagination.count}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">已付款</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {applications.filter(app => app.payment_status === 'paid').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">待审核</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {applications.filter(app => app.audit_status === 'pending').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Eye className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">已通过</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {applications.filter(app => app.audit_status === 'approved').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-4 items-end">
              <div className="flex-1 min-w-[200px]">
                <Label htmlFor="search">搜索</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="搜索姓名、手机、公司..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="min-w-[150px]">
                <Label>支付状态</Label>
                <Select value={paymentStatusFilter} onValueChange={setPaymentStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="全部" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="paid">已付款</SelectItem>
                    <SelectItem value="unpaid">未付款</SelectItem>
                    <SelectItem value="refunded">已退款</SelectItem>
                    <SelectItem value="cancelled">已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="min-w-[150px]">
                <Label>审核状态</Label>
                <Select value={auditStatusFilter} onValueChange={setAuditStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="全部" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="pending">待审核</SelectItem>
                    <SelectItem value="approved">已通过</SelectItem>
                    <SelectItem value="rejected">已拒绝</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button onClick={loadApplications} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 申请列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>代理申请列表</span>
              <Badge variant="outline">{pagination.count} 条记录</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>申请人</TableHead>
                    <TableHead>代理级别</TableHead>
                    <TableHead>联系方式</TableHead>
                    <TableHead>申请费用</TableHead>
                    <TableHead>支付状态</TableHead>
                    <TableHead>审核状态</TableHead>
                    <TableHead>申请时间</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    // 加载状态
                    Array.from({ length: 3 }).map((_, index) => (
                      <TableRow key={`loading-${index}`}>
                        <TableCell>
                          <div className="space-y-2">
                            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                            <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4"></div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                            <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3"></div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                        </TableCell>
                        <TableCell>
                          <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                        </TableCell>
                        <TableCell>
                          <div className="h-6 bg-gray-200 rounded animate-pulse w-16"></div>
                        </TableCell>
                        <TableCell>
                          <div className="h-6 bg-gray-200 rounded animate-pulse w-16"></div>
                        </TableCell>
                        <TableCell>
                          <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    applications.map((application) => (
                      <TableRow key={application.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{application.name}</div>
                            <div className="text-sm text-gray-500">{application.user_email}</div>
                            {application.company && (
                              <div className="text-sm text-gray-500 flex items-center">
                                <Building className="h-3 w-3 mr-1" />
                                {application.company}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{application.agent_level.name}</div>
                            <div className="text-sm text-gray-500">
                              分成比例: {application.agent_level.commission_rate}%
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {application.phone}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">¥{parseFloat(application.amount).toLocaleString()}</div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(application.payment_status, 'payment')}
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {getStatusBadge(application.audit_status, 'audit')}
                            {application.audit_status === 'rejected' && application.audit_reason && (
                              <div className="text-xs text-red-600 max-w-[200px] truncate" title={application.audit_reason}>
                                {application.audit_reason}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(application.created_at).toLocaleDateString('zh-CN')}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {applications.length === 0 && !loading && (
              <div className="text-center py-8 text-gray-500">
                <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>暂无代理申请记录</p>
                <p className="text-sm mt-1">通过您的域名提交的代理申请将在这里显示</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
