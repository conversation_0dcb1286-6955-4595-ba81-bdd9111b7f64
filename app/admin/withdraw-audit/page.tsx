"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import {
  CheckCircle,
  XCircle,
  Clock,
  Banknote,
  Loader2,
  AlertTriangle,
  Eye
} from "lucide-react"

// 临时类型定义（实际应该从API文件导入）
interface WithdrawRequest {
  id: number
  agent_user: {
    id: number
    email: string
  }
  withdraw_amount: number
  fee_rate: number
  fee_amount: number
  actual_amount: number
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  status_display: string
  payment_method: 'bank' | 'wechat' | 'alipay'
  payment_info_snapshot: any
  created_at: string
  reviewed_at?: string
  reject_reason?: string
}

export default function WithdrawAuditPage() {
  const [requests, setRequests] = useState<WithdrawRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedRequest, setSelectedRequest] = useState<WithdrawRequest | null>(null)
  const [showReviewDialog, setShowReviewDialog] = useState(false)
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve')
  const [rejectReason, setRejectReason] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const { toast } = useToast()

  // 获取提现申请列表
  const fetchRequests = async () => {
    try {
      setLoading(true)
      // 这里应该调用真实的API
      // const response = await getWithdrawRequestsAdmin({ status: statusFilter === 'all' ? undefined : statusFilter })
      
      // 临时mock数据
      const mockData: WithdrawRequest[] = [
        {
          id: 1,
          agent_user: { id: 123, email: "<EMAIL>" },
          withdraw_amount: 1000,
          fee_rate: 2.0,
          fee_amount: 20,
          actual_amount: 980,
          status: 'pending',
          status_display: '待审核',
          payment_method: 'bank',
          payment_info_snapshot: {
            bank_name: '中国银行',
            bank_account: '6217***********1234',
            account_holder: '张三'
          },
          created_at: new Date().toISOString()
        }
      ]
      
      setRequests(mockData)
    } catch (error) {
      toast({
        title: "获取提现申请失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 审核提现申请
  const handleReview = async () => {
    if (!selectedRequest) return

    try {
      // 这里应该调用真实的API
      // await reviewWithdrawRequest(selectedRequest.id, { action: reviewAction, reject_reason: rejectReason })
      
      toast({
        title: reviewAction === 'approve' ? "申请已通过" : "申请已拒绝",
        description: reviewAction === 'approve' ? "提现申请审核通过" : "提现申请已拒绝",
      })
      
      setShowReviewDialog(false)
      setSelectedRequest(null)
      setRejectReason('')
      fetchRequests()
      
    } catch (error) {
      toast({
        title: "审核失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    fetchRequests()
  }, [statusFilter])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" />
            待审核
          </Badge>
        )
      case 'approved':
        return (
          <Badge className="bg-blue-100 text-blue-700 border-blue-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            已通过
          </Badge>
        )
      case 'rejected':
        return (
          <Badge className="bg-red-100 text-red-700 border-red-200">
            <XCircle className="w-3 h-3 mr-1" />
            已拒绝
          </Badge>
        )
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            已打款
          </Badge>
        )
      default:
        return null
    }
  }

  const getPaymentMethodName = (method: string) => {
    const names = {
      'bank': '银行卡',
      'wechat': '微信',
      'alipay': '支付宝'
    }
    return names[method as keyof typeof names] || method
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">提现申请审核</h1>
          <p className="text-gray-600">管理代理商的提现申请，进行审核和打款操作</p>
        </div>

        {/* 筛选器 */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Label htmlFor="status-filter">状态筛选</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="pending">待审核</SelectItem>
                    <SelectItem value="approved">已通过</SelectItem>
                    <SelectItem value="rejected">已拒绝</SelectItem>
                    <SelectItem value="completed">已打款</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={fetchRequests} variant="outline">
                刷新
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 提现申请列表 */}
        <Card>
          <CardHeader>
            <CardTitle>提现申请列表</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-6 w-6 animate-spin text-orange-500" />
                <span className="ml-2 text-gray-600">加载中...</span>
              </div>
            ) : requests.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        代理商
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        提现金额
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        手续费
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        实际到账
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        收款方式
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        申请时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {requests.map((request) => (
                      <tr key={request.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {request.agent_user.email}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            ¥{request.withdraw_amount.toLocaleString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            ¥{request.fee_amount.toFixed(2)} ({request.fee_rate}%)
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-green-600">
                            ¥{request.actual_amount.toFixed(2)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {getPaymentMethodName(request.payment_method)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(request.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {new Date(request.created_at).toLocaleString('zh-CN')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedRequest(request)
                                // 这里可以显示详情对话框
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {request.status === 'pending' && (
                              <>
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    setSelectedRequest(request)
                                    setReviewAction('approve')
                                    setShowReviewDialog(true)
                                  }}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  通过
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => {
                                    setSelectedRequest(request)
                                    setReviewAction('reject')
                                    setShowReviewDialog(true)
                                  }}
                                >
                                  拒绝
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <Banknote className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">暂无提现申请</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 审核对话框 */}
        <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {reviewAction === 'approve' ? '通过提现申请' : '拒绝提现申请'}
              </DialogTitle>
              <DialogDescription>
                {selectedRequest && (
                  <div className="space-y-2 mt-4">
                    <p><strong>代理商：</strong>{selectedRequest.agent_user.email}</p>
                    <p><strong>提现金额：</strong>¥{selectedRequest.withdraw_amount.toLocaleString()}</p>
                    <p><strong>实际到账：</strong>¥{selectedRequest.actual_amount.toFixed(2)}</p>
                  </div>
                )}
              </DialogDescription>
            </DialogHeader>
            
            {reviewAction === 'reject' && (
              <div className="space-y-2">
                <Label htmlFor="reject-reason">拒绝原因</Label>
                <Textarea
                  id="reject-reason"
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  placeholder="请输入拒绝原因（可选）"
                  rows={3}
                />
              </div>
            )}
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowReviewDialog(false)}>
                取消
              </Button>
              <Button
                onClick={handleReview}
                className={reviewAction === 'approve' ? 'bg-green-600 hover:bg-green-700' : ''}
                variant={reviewAction === 'reject' ? 'destructive' : 'default'}
              >
                {reviewAction === 'approve' ? '确认通过' : '确认拒绝'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
