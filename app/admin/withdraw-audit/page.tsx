"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { config } from "@/lib/config"
import {
  CheckCircle,
  XCircle,
  Clock,
  Banknote,
  Loader2,
  Eye,
  ArrowLeft,
  RefreshCw
} from "lucide-react"

interface WithdrawRequest {
  id: number
  agent_user: {
    id: number
    email: string
    points: number
  }
  withdraw_amount: string
  fee_rate: string
  fee_amount: string
  actual_amount: string
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  status_display: string
  payment_method: 'bank' | 'wechat' | 'alipay'
  payment_info_snapshot: any
  created_at: string
  reviewed_by?: {
    id: number
    email: string
    points: number
  }
  reviewed_at?: string
  reject_reason?: string
  completed_by?: {
    id: number
    email: string
    points: number
  }
  completed_at?: string
  payment_reference?: string
}

interface PaginatedResponse<T> {
  count: number
  next: string | null
  previous: string | null
  results: T[]
}

export default function WithdrawAuditPage() {
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [requests, setRequests] = useState<PaginatedResponse<WithdrawRequest> | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedRequest, setSelectedRequest] = useState<WithdrawRequest | null>(null)
  const [showReviewDialog, setShowReviewDialog] = useState(false)
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve')
  const [rejectReason, setRejectReason] = useState('')
  const [paymentReference, setPaymentReference] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [processing, setProcessing] = useState(false)
  const { toast } = useToast()

  // 检查管理员权限
  useEffect(() => {
    const checkAdminAccess = async () => {
      try {
        const token = localStorage.getItem('access_token')
        if (!token) {
          router.push('/login')
          return
        }

        const response = await fetch(`${config.API_BASE_URL}/auth/user`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success && data.data?.user?.is_staff) {
            setIsAuthorized(true)
            fetchRequests()
          } else {
            router.push('/')
          }
        } else {
          router.push('/login')
        }
      } catch (error) {
        console.error('检查管理员权限失败:', error)
        router.push('/login')
      }
    }

    checkAdminAccess()
  }, [router])

  // 获取提现申请列表
  const fetchRequests = async (page = 1) => {
    try {
      setLoading(true)
      const token = localStorage.getItem('access_token')
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: '20'
      })

      if (statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      const response = await fetch(`${config.API_BASE_URL}/agent/admin/withdraw/admin/list/?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()

        if (data.success) {
          // 后端返回的数据结构是 { success: true, data: { results: [...], count: ..., next: ..., previous: ... } }
          setRequests(data.data)
          setCurrentPage(page)
        } else {
          toast({
            title: "获取提现申请失败",
            description: data.error || "请稍后重试",
            variant: "destructive",
          })
        }
      } else {
        throw new Error('获取提现申请失败')
      }
    } catch (error) {
      toast({
        title: "获取提现申请失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 审核提现申请
  const handleReview = async () => {
    if (!selectedRequest) return

    try {
      setProcessing(true)
      const token = localStorage.getItem('access_token')

      const requestData: any = {
        action: reviewAction
      }

      if (reviewAction === 'reject' && rejectReason) {
        requestData.reject_reason = rejectReason
      }

      // 注意：payment_reference 需要在审核通过后单独调用完成API来设置

      const response = await fetch(`${config.API_BASE_URL}/agent/admin/withdraw/admin/${selectedRequest.id}/review/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // 如果是通过审核且有打款凭证，需要调用完成API
          if (reviewAction === 'approve' && paymentReference) {
            try {
              const completeResponse = await fetch(`${config.API_BASE_URL}/agent/admin/withdraw/admin/${selectedRequest.id}/complete/`, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  payment_reference: paymentReference
                })
              })

              if (completeResponse.ok) {
                const completeData = await completeResponse.json()
                if (completeData.success) {
                  toast({
                    title: "审核成功",
                    description: "提现申请已通过并标记为已打款",
                  })
                } else {
                  toast({
                    title: "审核成功，但打款标记失败",
                    description: completeData.error || "请手动标记打款状态",
                    variant: "destructive",
                  })
                }
              }
            } catch (error) {
              console.error('Complete withdraw error:', error)
              toast({
                title: "审核成功，但打款标记失败",
                description: "请手动标记打款状态",
                variant: "destructive",
              })
            }
          } else {
            toast({
              title: "审核成功",
              description: reviewAction === 'approve' ? "提现申请已通过" : "提现申请已拒绝",
            })
          }

          setShowReviewDialog(false)
          setSelectedRequest(null)
          setRejectReason('')
          setPaymentReference('')
          fetchRequests(currentPage)
        } else {
          toast({
            title: "审核失败",
            description: data.error || "请稍后重试",
            variant: "destructive",
          })
        }
      } else {
        throw new Error('审核失败')
      }
    } catch (error) {
      toast({
        title: "审核失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setProcessing(false)
    }
  }

  useEffect(() => {
    if (isAuthorized) {
      fetchRequests(1)
    }
  }, [statusFilter, isAuthorized])

  // 格式化金额
  const formatAmount = (amount: string | number) => {
    return Number(amount || 0).toFixed(2)
  }

  // 格式化时间
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" />
            待审核
          </Badge>
        )
      case 'approved':
        return (
          <Badge className="bg-blue-100 text-blue-700 border-blue-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            已通过
          </Badge>
        )
      case 'rejected':
        return (
          <Badge className="bg-red-100 text-red-700 border-red-200">
            <XCircle className="w-3 h-3 mr-1" />
            已拒绝
          </Badge>
        )
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            已打款
          </Badge>
        )
      default:
        return null
    }
  }

  const getPaymentMethodName = (method: string) => {
    const names = {
      'bank': '银行卡',
      'wechat': '微信',
      'alipay': '支付宝'
    }
    return names[method as keyof typeof names] || method
  }

  if (loading && !isAuthorized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!isAuthorized) {
    return <div></div>
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <header className="border-b border-gray-200/50 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/admin')}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>返回管理后台</span>
              </Button>
              <div className="h-4 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">提现审核</h1>
                <p className="text-sm text-gray-600">管理代理商提现申请</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        {/* 筛选器 */}
        <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl mb-4">
          <CardContent className="pt-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Label htmlFor="status-filter">状态筛选</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="pending">待审核</SelectItem>
                    <SelectItem value="approved">已通过</SelectItem>
                    <SelectItem value="rejected">已拒绝</SelectItem>
                    <SelectItem value="completed">已打款</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={() => fetchRequests(currentPage)} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 提现申请列表 */}
        <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>提现申请列表</span>
              {requests && (
                <span className="text-sm text-gray-500">
                  共 {requests.count} 条记录
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
                <span className="ml-2 text-gray-600">加载中...</span>
              </div>
            ) : requests && requests.results.length > 0 ? (
              <div className="space-y-4">
                {requests.results.map((request) => (
                  <div
                    key={request.id}
                    className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{request.agent_user.email}</span>
                          </div>
                          {getStatusBadge(request.status)}
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                          <div>
                            <span className="text-gray-500">提现金额：</span>
                            <span className="font-medium">¥{formatAmount(request.withdraw_amount)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">手续费：</span>
                            <span>¥{formatAmount(request.fee_amount)} ({Number(request.fee_rate).toFixed(1)}%)</span>
                          </div>
                          <div>
                            <span className="text-gray-500">实际到账：</span>
                            <span className="font-medium text-green-600">¥{formatAmount(request.actual_amount)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">申请时间：</span>
                            <span>{formatDate(request.created_at)}</span>
                          </div>
                        </div>

                        <div className="text-sm text-gray-600">
                          <span className="text-gray-500">收款方式：</span>
                          <span>{getPaymentMethodName(request.payment_method)}</span>
                        </div>

                        {request.reject_reason && (
                          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm">
                            <span className="text-red-600 font-medium">拒绝原因：</span>
                            <span className="text-red-700">{request.reject_reason}</span>
                          </div>
                        )}

                        {request.payment_reference && (
                          <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm">
                            <span className="text-green-600 font-medium">打款凭证：</span>
                            <span className="text-green-700">{request.payment_reference}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col gap-2 ml-4">
                        {request.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              onClick={() => {
                                setSelectedRequest(request)
                                setReviewAction('approve')
                                setShowReviewDialog(true)
                              }}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              通过
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => {
                                setSelectedRequest(request)
                                setReviewAction('reject')
                                setShowReviewDialog(true)
                              }}
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              拒绝
                            </Button>
                          </>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedRequest(request)
                            setShowDetailDialog(true)
                          }}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          详情
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

                {/* 分页控制 */}
                {requests && requests.count > 20 && (
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200 mt-4">
                    <div className="text-sm text-gray-600">
                      第 {currentPage} 页，共 {Math.ceil(requests.count / 20)} 页，总计 {requests.count} 条记录
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchRequests(currentPage - 1)}
                        disabled={!requests.previous}
                      >
                        上一页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchRequests(currentPage + 1)}
                        disabled={!requests.next}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <Banknote className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-2">暂无提现申请</p>
                <p className="text-sm text-gray-400">当前没有需要审核的提现申请</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 审核对话框 */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {reviewAction === 'approve' ? '通过提现申请' : '拒绝提现申请'}
            </DialogTitle>
            <DialogDescription>
              {selectedRequest && (
                <>
                  用户：{selectedRequest.agent_user.email}<br />
                  提现金额：¥{formatAmount(selectedRequest.withdraw_amount)}<br />
                  实际到账：¥{formatAmount(selectedRequest.actual_amount)}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {reviewAction === 'reject' ? (
              <div>
                <Label htmlFor="rejectReason">拒绝原因</Label>
                <Textarea
                  id="rejectReason"
                  placeholder="请输入拒绝原因"
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  rows={3}
                />
              </div>
            ) : (
              <div>
                <Label htmlFor="paymentReference">打款凭证（可选）</Label>
                <Input
                  id="paymentReference"
                  placeholder="请输入打款凭证号或备注"
                  value={paymentReference}
                  onChange={(e) => setPaymentReference(e.target.value)}
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReviewDialog(false)}>
              取消
            </Button>
            <Button
              onClick={handleReview}
              disabled={processing || (reviewAction === 'reject' && !rejectReason.trim())}
              className={reviewAction === 'approve' ? 'bg-green-600 hover:bg-green-700' : ''}
              variant={reviewAction === 'reject' ? 'destructive' : 'default'}
            >
              {processing ? '处理中...' : reviewAction === 'approve' ? '确认通过' : '确认拒绝'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 详情对话框 */}
      <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>提现申请详情</DialogTitle>
            <DialogDescription>
              查看提现申请的详细信息和收款方式
            </DialogDescription>
          </DialogHeader>

          {selectedRequest && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">申请人</Label>
                  <p className="mt-1 text-sm text-gray-900">{selectedRequest.agent_user.email}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">申请时间</Label>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(selectedRequest.created_at)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">提现金额</Label>
                  <p className="mt-1 text-sm font-semibold text-gray-900">¥{formatAmount(selectedRequest.withdraw_amount)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">手续费</Label>
                  <p className="mt-1 text-sm text-gray-900">¥{formatAmount(selectedRequest.fee_amount)} ({Number(selectedRequest.fee_rate).toFixed(1)}%)</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">实际到账</Label>
                  <p className="mt-1 text-sm font-semibold text-green-600">¥{formatAmount(selectedRequest.actual_amount)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">当前状态</Label>
                  <div className="mt-1">{getStatusBadge(selectedRequest.status)}</div>
                </div>
              </div>

              {/* 收款信息 */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-3 block">收款信息</Label>
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">收款方式</Label>
                    <p className="mt-1 text-sm text-gray-900">{getPaymentMethodName(selectedRequest.payment_method)}</p>
                  </div>

                  {selectedRequest.payment_info_snapshot && (
                    <div className="space-y-3">
                      {/* 银行卡信息 */}
                      {selectedRequest.payment_info_snapshot.bank_name && (
                        <div className="border border-gray-200 rounded-lg p-3 bg-white">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">银行卡信息</h4>
                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="text-gray-600">银行名称：</span>
                              <span className="text-gray-900">{selectedRequest.payment_info_snapshot.bank_name}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">账户名：</span>
                              <span className="text-gray-900">{selectedRequest.payment_info_snapshot.account_holder}</span>
                            </div>
                            <div className="col-span-2">
                              <span className="text-gray-600">银行账号：</span>
                              <span className="text-gray-900 font-mono">{selectedRequest.payment_info_snapshot.bank_account}</span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 微信收款码 */}
                      {selectedRequest.payment_info_snapshot.wechat_qr_code && (
                        <div className="border border-gray-200 rounded-lg p-3 bg-white">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">微信收款码</h4>
                          <div className="flex items-center space-x-3">
                            <img
                              src={selectedRequest.payment_info_snapshot.wechat_qr_code}
                              alt="微信收款码"
                              className="w-20 h-20 object-cover rounded border"
                            />
                            <div className="text-sm text-gray-600">
                              <p>微信收款二维码</p>
                              <p className="text-xs text-gray-500 mt-1">扫码转账到微信</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 支付宝收款码 */}
                      {selectedRequest.payment_info_snapshot.alipay_qr_code && (
                        <div className="border border-gray-200 rounded-lg p-3 bg-white">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">支付宝收款码</h4>
                          <div className="flex items-center space-x-3">
                            <img
                              src={selectedRequest.payment_info_snapshot.alipay_qr_code}
                              alt="支付宝收款码"
                              className="w-20 h-20 object-cover rounded border"
                            />
                            <div className="text-sm text-gray-600">
                              <p>支付宝收款二维码</p>
                              <p className="text-xs text-gray-500 mt-1">扫码转账到支付宝</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 联系电话 */}
                      {selectedRequest.payment_info_snapshot.contact_phone && (
                        <div className="border border-gray-200 rounded-lg p-3 bg-white">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">联系方式</h4>
                          <div className="text-sm">
                            <span className="text-gray-600">联系电话：</span>
                            <span className="text-gray-900">{selectedRequest.payment_info_snapshot.contact_phone}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* 审核信息 */}
              {(selectedRequest.reviewed_by || selectedRequest.reject_reason || selectedRequest.payment_reference) && (
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-3 block">审核信息</Label>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                    {selectedRequest.reviewed_by && (
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div>
                          <span className="text-gray-600">审核人：</span>
                          <span className="text-gray-900">{selectedRequest.reviewed_by.email}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">审核时间：</span>
                          <span className="text-gray-900">{selectedRequest.reviewed_at ? formatDate(selectedRequest.reviewed_at) : '-'}</span>
                        </div>
                      </div>
                    )}

                    {selectedRequest.reject_reason && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded">
                        <span className="text-sm font-medium text-red-700">拒绝原因：</span>
                        <p className="text-sm text-red-600 mt-1">{selectedRequest.reject_reason}</p>
                      </div>
                    )}

                    {selectedRequest.payment_reference && (
                      <div className="p-3 bg-green-50 border border-green-200 rounded">
                        <span className="text-sm font-medium text-green-700">打款凭证：</span>
                        <p className="text-sm text-green-600 mt-1">{selectedRequest.payment_reference}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDetailDialog(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
