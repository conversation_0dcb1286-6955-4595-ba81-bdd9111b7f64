"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Shield, ExternalLink, Users, Settings, BarChart3, Database } from "lucide-react"
import { config } from "@/lib/config"

export default function AdminPage() {
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 检查用户是否为管理员
    const checkAdminAccess = async () => {
      try {
        const token = localStorage.getItem('access_token')
        if (!token) {
          setIsLoading(false)
          return
        }

        const response = await fetch(`${config.API_BASE_URL}/auth/user`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success && data.data?.user?.is_staff) {
            setIsAuthorized(true)
          }
        }
      } catch (error) {
        console.error('检查管理员权限失败:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAdminAccess()
  }, [router])

  // 如果正在加载或没有权限，显示空白页
  if (isLoading || !isAuthorized) {
    return <div></div>
  }

  const openDjangoAdmin = () => {
    window.open(`${config.API_BASE_URL.replace('/api', '')}/admin/`, '_blank')
  }

  const navigateToAgentManagement = () => {
    router.push('/admin/agent-management')
  }

  const navigateToAgentAudit = () => {
    router.push('/admin/agent-audit')
  }

  const navigateToContactSettings = () => {
    router.push('/admin/contact-settings')
  }

  const navigateToWithdrawAudit = () => {
    router.push('/admin/withdraw-audit')
  }

  const adminFeatures = [
    {
      title: "用户管理",
      description: "管理用户账户、积分、权限等",
      icon: Users,
      color: "bg-blue-50 text-blue-600",
      items: ["用户列表", "积分历史", "权限管理"],
      action: null
    },
    {
      title: "代理商管理",
      description: "管理代理商等级、分成比例、域名绑定",
      icon: BarChart3,
      color: "bg-green-50 text-green-600",
      items: ["代理商等级", "用户代理关系", "分成记录"],
      action: navigateToAgentManagement
    },
    {
      title: "代理审核",
      description: "审核代理申请订单，管理代理升级",
      icon: Shield,
      color: "bg-yellow-50 text-yellow-600",
      items: ["申请审核", "升级管理", "拒绝处理"],
      action: navigateToAgentAudit
    },
    {
      title: "联系方式配置",
      description: "配置总部联系方式和微信二维码",
      icon: Settings,
      color: "bg-purple-50 text-purple-600",
      items: ["微信二维码", "联系电话", "显示设置"],
      action: navigateToContactSettings
    },
    {
      title: "提现审核",
      description: "审核代理商提现申请，管理提现流程",
      icon: Database,
      color: "bg-red-50 text-red-600",
      items: ["提现申请", "审核处理", "打款管理"],
      action: navigateToWithdrawAudit
    },
    {
      title: "订单管理",
      description: "查看和管理降AIGC订单",
      icon: Database,
      color: "bg-indigo-50 text-indigo-600",
      items: ["订单列表", "状态管理", "批量操作"],
      action: null
    },
    {
      title: "系统设置",
      description: "配置系统参数、价格、积分等",
      icon: Settings,
      color: "bg-orange-50 text-orange-600",
      items: ["注册积分", "服务价格", "维护模式"],
      action: null
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <header className="border-b border-gray-200/50 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">管理员后台</h1>
                <p className="text-gray-600">系统管理和配置中心</p>
              </div>
            </div>
            <Button onClick={openDjangoAdmin} className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600">
              <ExternalLink className="h-4 w-4 mr-2" />
              打开Django后台
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Card */}
        <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl mb-8">
          <CardContent className="p-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">欢迎使用管理员后台</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                这里是系统的管理中心，您可以管理用户、代理商、订单和系统设置。
                点击下方的功能模块或使用Django后台进行详细管理。
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Feature Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {adminFeatures.map((feature, index) => {
            const Icon = feature.icon

            return (
              <Card
                key={index}
                className="bg-white/90 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer group"
                onClick={feature.action || undefined}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${feature.color}`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {feature.title}
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        {feature.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {feature.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex items-center space-x-2 text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                        <span>{item}</span>
                      </div>
                    ))}
                  </div>
                  {feature.action ? (
                    <Button
                      variant="default"
                      className="w-full mt-4 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
                      onClick={feature.action}
                    >
                      进入管理
                      <ExternalLink className="h-4 w-4 ml-2" />
                    </Button>
                  ) : (
                    <Button
                      variant="ghost"
                      className="w-full mt-4 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      onClick={openDjangoAdmin}
                    >
                      在Django后台管理
                      <ExternalLink className="h-4 w-4 ml-2" />
                    </Button>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Quick Actions */}
        <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900">快速操作</CardTitle>
            <CardDescription>常用的管理操作</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center space-y-2 border-blue-200 hover:bg-blue-50"
                onClick={openDjangoAdmin}
              >
                <Users className="h-6 w-6 text-blue-600" />
                <span className="font-medium">用户管理</span>
                <span className="text-xs text-gray-500">管理用户账户和权限</span>
              </Button>
              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2 border-green-200 hover:bg-green-50"
                onClick={navigateToAgentManagement}
              >
                <BarChart3 className="h-6 w-6 text-green-600" />
                <span className="font-medium">代理商管理</span>
                <span className="text-xs text-gray-500">管理代理商和分成</span>
              </Button>
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center space-y-2 border-orange-200 hover:bg-orange-50"
                onClick={openDjangoAdmin}
              >
                <Settings className="h-6 w-6 text-orange-600" />
                <span className="font-medium">系统设置</span>
                <span className="text-xs text-gray-500">配置系统参数</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Info Card */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 mt-8">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                <Shield className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-blue-900 mb-1">关于Django后台</h3>
                <p className="text-blue-700 text-sm">
                  Django后台提供了完整的数据管理功能，包括用户管理、代理商管理、订单管理和系统设置。
                  所有的数据操作都在Django后台进行，确保数据的安全性和一致性。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
