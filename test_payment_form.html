<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收款信息表单验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 12px;
        }
        .form-group input {
            width: 100%;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            height: 32px;
            font-size: 14px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .upload-area:hover {
            border-color: #999;
        }
        .preview-image {
            width: 96px;
            height: 96px;
            object-fit: cover;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 0 auto;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.2s;
        }
        .button:hover {
            background: #005a8b;
        }
        .button:disabled {
            background: #9ca3af;
            color: #6b7280;
            cursor: not-allowed;
        }
        .button.enabled {
            background: #2563eb;
        }
        .button.enabled:hover {
            background: #1d4ed8;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status.valid {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.invalid {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 收款信息表单验证测试</h1>
    
    <div class="container">
        <h2>收款信息配置</h2>
        
        <!-- 银行卡信息 -->
        <div>
            <h4>银行卡信息</h4>
            <div class="grid">
                <div class="form-group">
                    <label>银行名称</label>
                    <input type="text" id="bankName" placeholder="如：中国银行">
                </div>
                <div class="form-group">
                    <label>开户人姓名</label>
                    <input type="text" id="accountHolder" placeholder="请输入开户人姓名">
                </div>
            </div>
            <div class="form-group">
                <label>银行卡号</label>
                <input type="text" id="bankAccount" placeholder="请输入银行卡号">
            </div>
        </div>

        <!-- 电子收款码 -->
        <div>
            <h4>电子收款码</h4>
            <div class="grid">
                <div>
                    <label>微信收款码</label>
                    <div id="wechatUpload" class="upload-area" onclick="document.getElementById('wechatFile').click()">
                        <div id="wechatPreview">
                            <div>📱</div>
                            <div style="font-size: 12px;">上传微信码</div>
                        </div>
                    </div>
                    <input type="file" id="wechatFile" style="display: none" accept="image/*">
                </div>
                <div>
                    <label>支付宝收款码</label>
                    <div id="alipayUpload" class="upload-area" onclick="document.getElementById('alipayFile').click()">
                        <div id="alipayPreview">
                            <div>💰</div>
                            <div style="font-size: 12px;">上传支付宝码</div>
                        </div>
                    </div>
                    <input type="file" id="alipayFile" style="display: none" accept="image/*">
                </div>
            </div>
        </div>

        <!-- 联系信息 -->
        <div class="form-group">
            <label>联系电话</label>
            <input type="text" id="contactPhone" placeholder="请输入联系电话">
        </div>

        <!-- 验证状态显示 -->
        <div id="validationStatus" class="status invalid">
            请至少配置一种收款方式
        </div>

        <!-- 调试信息 -->
        <div id="debugInfo" class="debug">
            <strong>调试信息：</strong><br>
            <span id="debugText">等待输入...</span>
        </div>

        <!-- 保存按钮 -->
        <button id="saveButton" class="button" disabled>保存</button>
    </div>

    <script>
        // 表单数据
        let formData = {
            bank_name: '',
            bank_account: '',
            account_holder: '',
            wechat_qr_code: '',
            alipay_qr_code: '',
            contact_phone: ''
        };

        // 验证表单是否有效
        function isFormValid() {
            // 银行卡信息完整
            const bankComplete = formData.bank_name?.trim() && 
                                formData.bank_account?.trim() && 
                                formData.account_holder?.trim();
            
            // 或者有微信收款码
            const hasWechat = formData.wechat_qr_code?.trim();
            
            // 或者有支付宝收款码
            const hasAlipay = formData.alipay_qr_code?.trim();
            
            return Boolean(bankComplete || hasWechat || hasAlipay);
        }

        // 更新UI状态
        function updateUI() {
            const valid = isFormValid();
            const saveButton = document.getElementById('saveButton');
            const statusDiv = document.getElementById('validationStatus');
            const debugDiv = document.getElementById('debugText');

            // 更新保存按钮
            if (valid) {
                saveButton.disabled = false;
                saveButton.className = 'button enabled';
                statusDiv.className = 'status valid';
                statusDiv.textContent = '✅ 收款信息配置完整，可以保存';
            } else {
                saveButton.disabled = true;
                saveButton.className = 'button';
                statusDiv.className = 'status invalid';
                statusDiv.textContent = '⚠️ 请至少配置一种收款方式';
            }

            // 更新调试信息
            const bankComplete = formData.bank_name?.trim() && 
                                formData.bank_account?.trim() && 
                                formData.account_holder?.trim();
            
            debugDiv.innerHTML = `
                银行卡完整: ${bankComplete ? '是' : '否'}<br>
                微信收款码: ${formData.wechat_qr_code ? '已上传' : '未上传'}<br>
                支付宝收款码: ${formData.alipay_qr_code ? '已上传' : '未上传'}<br>
                表单有效: ${valid ? '是' : '否'}
            `;
        }

        // 处理图片上传
        function handleImageUpload(file, type) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const base64 = e.target.result;
                formData[type === 'wechat' ? 'wechat_qr_code' : 'alipay_qr_code'] = base64;
                
                // 显示预览
                const previewId = type === 'wechat' ? 'wechatPreview' : 'alipayPreview';
                const preview = document.getElementById(previewId);
                preview.innerHTML = `
                    <img src="${base64}" class="preview-image" alt="${type}收款码">
                    <button onclick="clearImage('${type}')" style="margin-top: 5px; font-size: 12px;">删除</button>
                `;
                
                updateUI();
            };
            reader.readAsDataURL(file);
        }

        // 清除图片
        function clearImage(type) {
            formData[type === 'wechat' ? 'wechat_qr_code' : 'alipay_qr_code'] = '';
            const previewId = type === 'wechat' ? 'wechatPreview' : 'alipayPreview';
            const preview = document.getElementById(previewId);
            const icon = type === 'wechat' ? '📱' : '💰';
            const text = type === 'wechat' ? '上传微信码' : '上传支付宝码';
            preview.innerHTML = `
                <div>${icon}</div>
                <div style="font-size: 12px;">${text}</div>
            `;
            updateUI();
        }

        // 绑定事件
        document.getElementById('bankName').addEventListener('input', function(e) {
            formData.bank_name = e.target.value;
            updateUI();
        });

        document.getElementById('bankAccount').addEventListener('input', function(e) {
            formData.bank_account = e.target.value;
            updateUI();
        });

        document.getElementById('accountHolder').addEventListener('input', function(e) {
            formData.account_holder = e.target.value;
            updateUI();
        });

        document.getElementById('contactPhone').addEventListener('input', function(e) {
            formData.contact_phone = e.target.value;
            updateUI();
        });

        document.getElementById('wechatFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                handleImageUpload(file, 'wechat');
            }
        });

        document.getElementById('alipayFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                handleImageUpload(file, 'alipay');
            }
        });

        document.getElementById('saveButton').addEventListener('click', function() {
            if (isFormValid()) {
                alert('✅ 保存成功！\n\n' + JSON.stringify(formData, null, 2));
            } else {
                alert('❌ 表单验证失败，请检查输入');
            }
        });

        // 初始化
        updateUI();
    </script>
</body>
</html>
