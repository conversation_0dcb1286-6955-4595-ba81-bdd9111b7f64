# Generated by Django 5.2.4 on 2025-07-18 07:34

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system_settings', '0003_systemsettings_mock_payment_enabled'),
    ]

    operations = [
        migrations.AddField(
            model_name='systemsettings',
            name='min_withdraw_amount',
            field=models.DecimalField(decimal_places=2, default=100, help_text='代理商单次提现的最低金额（元）', max_digits=10, validators=[django.core.validators.MinValueValidator(1)], verbose_name='最低提现金额'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='withdraw_fee_rate',
            field=models.DecimalField(decimal_places=2, default=2.0, help_text='提现手续费比例，单位为百分比（例如：2.0表示2%）', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='提现手续费比例'),
        ),
    ]
