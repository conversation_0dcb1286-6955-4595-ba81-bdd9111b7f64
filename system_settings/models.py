from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from decouple import config


class SystemSettings(models.Model):
    """
    系统设置模型 - 单例模式
    """
    # 注册相关设置
    register_points_enabled = models.BooleanField(
        default=True,
        verbose_name='是否开启注册赠送积分',
        help_text='新用户注册时是否自动赠送积分'
    )

    register_points_amount = models.PositiveIntegerField(
        default=config('DEFAULT_REGISTER_POINTS', default=500, cast=int),
        validators=[MinValueValidator(0)],
        verbose_name='注册赠送积分数量',
        help_text='新用户注册时赠送的积分数量'
    )

    # 积分相关设置
    points_system_enabled = models.BooleanField(
        default=False,
        verbose_name='是否开启积分系统',
        help_text='关闭后，系统将不显示任何积分相关功能'
    )

    points_to_yuan_rate = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        default=0.01,
        validators=[MinValueValidator(0)],
        verbose_name='积分兑换人民币汇率',
        help_text='1积分等于多少人民币，例如0.01表示1积分=0.01元'
    )

    # 充值相关设置
    min_recharge_amount = models.PositiveIntegerField(
        default=10,
        validators=[MinValueValidator(1)],
        verbose_name='最小充值金额',
        help_text='用户单次充值的最小金额（元）'
    )

    max_recharge_amount = models.PositiveIntegerField(
        default=10000,
        validators=[MinValueValidator(1)],
        verbose_name='最大充值金额',
        help_text='用户单次充值的最大金额（元）'
    )

    # 服务价格设置
    ai_rewrite_price_per_thousand = models.PositiveIntegerField(
        default=29,
        validators=[MinValueValidator(1)],
        verbose_name='AI优化价格（元/千字）',
        help_text='AI自动优化服务的价格，按千字计费'
    )

    manual_rewrite_price_per_thousand = models.PositiveIntegerField(
        default=99,
        validators=[MinValueValidator(1)],
        verbose_name='人工优化价格（元/千字）',
        help_text='人工专家优化服务的价格，按千字计费'
    )

    # 系统维护设置
    maintenance_mode = models.BooleanField(
        default=False,
        verbose_name='维护模式',
        help_text='开启后系统将进入维护模式，用户无法使用服务'
    )

    maintenance_message = models.TextField(
        blank=True,
        default='系统正在维护中，请稍后再试',
        verbose_name='维护提示信息',
        help_text='维护模式下显示给用户的提示信息'
    )

    # 模拟支付设置
    mock_payment_enabled = models.BooleanField(
        default=True,  # 开发阶段默认开启
        verbose_name='是否开启模拟支付',
        help_text='开启后允许使用模拟支付功能，关闭后所有模拟支付接口将被禁用（生产环境建议关闭）'
    )

    # 提现相关设置
    min_withdraw_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=100,
        validators=[MinValueValidator(1)],
        verbose_name='最低提现金额',
        help_text='代理商单次提现的最低金额（元）'
    )

    withdraw_fee_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=2.0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='提现手续费比例',
        help_text='提现手续费比例，单位为百分比（例如：2.0表示2%）'
    )

    # 更新时间
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )

    updated_by = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='更新人',
        help_text='最后更新设置的管理员'
    )

    class Meta:
        db_table = 'system_settings'
        verbose_name = '系统设置'
        verbose_name_plural = '系统设置'

    def __str__(self):
        return '系统设置'

    def save(self, *args, **kwargs):
        # 确保只有一条记录
        self.pk = 1
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        # 防止删除
        pass

    @classmethod
    def get_settings(cls):
        """获取系统设置（单例）"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings

    @classmethod
    def is_points_system_enabled(cls):
        """检查积分系统是否开启"""
        return cls.get_settings().points_system_enabled

    @classmethod
    def get_register_points(cls):
        """获取注册赠送积分数量"""
        settings = cls.get_settings()
        if settings.points_system_enabled and settings.register_points_enabled:
            return settings.register_points_amount
        return 0

    @classmethod
    def get_points_to_yuan_rate(cls):
        """获取积分兑换人民币汇率"""
        return cls.get_settings().points_to_yuan_rate

    @classmethod
    def get_service_prices(cls):
        """获取服务价格"""
        settings = cls.get_settings()
        return {
            'ai_rewrite': settings.ai_rewrite_price_per_thousand,
            'manual_rewrite': settings.manual_rewrite_price_per_thousand
        }

    @classmethod
    def is_maintenance_mode(cls):
        """检查是否为维护模式"""
        return cls.get_settings().maintenance_mode

    @classmethod
    def is_mock_payment_enabled(cls):
        """检查是否开启模拟支付"""
        return cls.get_settings().mock_payment_enabled

    @classmethod
    def get_withdraw_settings(cls):
        """获取提现相关设置"""
        settings = cls.get_settings()
        return {
            'min_withdraw_amount': settings.min_withdraw_amount,
            'withdraw_fee_rate': settings.withdraw_fee_rate
        }
