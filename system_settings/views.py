from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from .models import SystemSettings


@api_view(['GET'])
@permission_classes([AllowAny])
def get_public_settings(request):
    """获取公开的系统设置"""
    try:
        settings = SystemSettings.get_settings()

        data = {
            'points_system_enabled': settings.points_system_enabled,
            'register_points_amount': settings.get_register_points(),
            'maintenance_mode': settings.maintenance_mode,
            'maintenance_message': settings.maintenance_message,
            'mock_payment_enabled': settings.mock_payment_enabled,
            'service_prices': {
                'ai_rewrite_price_per_thousand': settings.ai_rewrite_price_per_thousand,
                'manual_rewrite_price_per_thousand': settings.manual_rewrite_price_per_thousand,
            },
            'withdraw_settings': settings.get_withdraw_settings()
        }

        return Response({
            'success': True,
            'data': data
        })

    except Exception as e:
        return Response(
            {'error': f'获取系统设置失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def get_mock_payment_status(request):
    """获取模拟支付开关状态"""
    try:
        enabled = SystemSettings.is_mock_payment_enabled()

        return Response({
            'success': True,
            'data': {
                'mock_payment_enabled': enabled
            }
        })

    except Exception as e:
        return Response(
            {'error': f'获取模拟支付状态失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
