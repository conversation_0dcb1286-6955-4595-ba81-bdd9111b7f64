from django.contrib import admin
from django.utils.html import format_html
from .models import SystemSettings


@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    """
    系统设置管理
    """
    list_display = [
        'id', 'register_points_display', 'points_rate_display',
        'ai_price_display', 'manual_price_display', 'recharge_range_display',
        'maintenance_status', 'mock_payment_status', 'updated_at', 'updated_by'
    ]

    fieldsets = (
        ('注册设置', {
            'fields': ('register_points_enabled', 'register_points_amount'),
            'description': '控制新用户注册时的积分赠送'
        }),
        ('积分设置', {
            'fields': ('points_to_yuan_rate', 'min_recharge_amount', 'max_recharge_amount'),
            'description': '积分与人民币的兑换比例和充值限制'
        }),
        ('服务价格', {
            'fields': ('ai_rewrite_price_per_thousand', 'manual_rewrite_price_per_thousand'),
            'description': '每千字的服务价格设置'
        }),
        ('系统维护', {
            'fields': ('maintenance_mode', 'maintenance_message'),
            'description': '系统维护模式和提示信息'
        }),
        ('模拟支付设置', {
            'fields': ('mock_payment_enabled',),
            'description': '控制模拟支付功能的开启和关闭（生产环境建议关闭）'
        }),
        ('提现设置', {
            'fields': ('min_withdraw_amount', 'withdraw_fee_rate'),
            'description': '代理商提现相关的配置参数'
        }),
        ('更新信息', {
            'fields': ('updated_by', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    readonly_fields = ['updated_at']

    def register_points_display(self, obj):
        """注册积分显示"""
        if obj.register_points_enabled:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">开启 ({}积分)</span>',
                obj.register_points_amount
            )
        return format_html('<span style="color: #dc3545;">关闭</span>')
    register_points_display.short_description = '注册赠送积分'

    def points_rate_display(self, obj):
        """积分汇率显示"""
        return format_html(
            '<span style="color: #007cba; font-weight: bold;">1积分 = ¥{}</span>',
            obj.points_to_yuan_rate
        )
    points_rate_display.short_description = '积分汇率'

    def ai_price_display(self, obj):
        """AI价格显示"""
        return format_html(
            '<span style="color: #007bff; font-weight: bold;">¥{}/千字</span>',
            f'{obj.ai_rewrite_price_per_thousand:.2f}'
        )
    ai_price_display.short_description = 'AI重写价格'

    def manual_price_display(self, obj):
        """人工价格显示"""
        return format_html(
            '<span style="color: #28a745; font-weight: bold;">¥{}/千字</span>',
            f'{obj.manual_rewrite_price_per_thousand:.2f}'
        )
    manual_price_display.short_description = '人工重写价格'

    def recharge_range_display(self, obj):
        """充值范围显示"""
        return format_html(
            '<span style="color: #6c757d;">¥{} - ¥{}</span>',
            obj.min_recharge_amount,
            obj.max_recharge_amount
        )
    recharge_range_display.short_description = '充值范围'

    def maintenance_status(self, obj):
        """维护状态显示"""
        if obj.maintenance_mode:
            return format_html('<span style="color: #dc3545; font-weight: bold;">维护中</span>')
        return format_html('<span style="color: #28a745; font-weight: bold;">正常</span>')
    maintenance_status.short_description = '系统状态'

    def mock_payment_status(self, obj):
        """模拟支付状态显示"""
        if obj.mock_payment_enabled:
            return format_html('<span style="color: #ffc107; font-weight: bold;">⚠️ 已开启</span>')
        return format_html('<span style="color: #28a745; font-weight: bold;">🔒 已关闭</span>')
    mock_payment_status.short_description = '模拟支付'

    def has_add_permission(self, request):
        # 只允许有一条记录
        return not SystemSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # 不允许删除
        return False

    def save_model(self, request, obj, form, change):
        # 记录更新人
        obj.updated_by = request.user.email if request.user.email else request.user.username
        super().save_model(request, obj, form, change)
