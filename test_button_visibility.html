<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮样式可见性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button-test {
            display: flex;
            gap: 12px;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .button {
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            min-width: 80px;
            height: 36px;
        }
        .button:hover {
            opacity: 0.9;
        }
        .button.outline {
            background: white;
            border: 1px solid #d1d5db;
            color: #374151;
        }
        .button.outline:hover {
            background: #f9fafb;
        }
        .button.enabled {
            background-color: #2563eb;
            color: white;
        }
        .button.disabled {
            background-color: #9ca3af;
            color: #4b5563;
            cursor: not-allowed;
        }
        .description {
            flex: 1;
            margin-left: 15px;
        }
        .status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;
        }
        .status.valid {
            background: #dcfce7;
            color: #166534;
        }
        .status.invalid {
            background: #fef2f2;
            color: #dc2626;
        }
        .debug-info {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            padding: 12px;
            border-radius: 6px;
            font-size: 14px;
            margin: 15px 0;
        }
        .color-box {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
            vertical-align: middle;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <h1>🎨 按钮样式可见性测试</h1>
    
    <div class="container">
        <h2>当前问题重现</h2>
        <p>用户反馈：保存按钮在取消按钮右边，但是样式和配色看不见</p>
        
        <div class="button-test">
            <button class="button outline">取消</button>
            <button class="button disabled">保存</button>
            <div class="description">
                <strong>问题状态：</strong>保存按钮可能因为样式问题看不清楚
                <span class="status invalid">有问题</span>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>修复后的按钮样式</h2>
        
        <div class="button-test">
            <button class="button outline">取消</button>
            <button class="button disabled">保存 (禁用)</button>
            <div class="description">
                <strong>禁用状态：</strong>表单未填写完整时
                <span class="status invalid">禁用</span>
                <div class="debug-info">
                    <div><span class="color-box" style="background-color: #9ca3af;"></span>背景色: #9ca3af (中等灰色)</div>
                    <div><span class="color-box" style="background-color: #4b5563;"></span>文字色: #4b5563 (深灰色)</div>
                </div>
            </div>
        </div>

        <div class="button-test">
            <button class="button outline">取消</button>
            <button class="button enabled">保存 (启用)</button>
            <div class="description">
                <strong>启用状态：</strong>表单填写完整时
                <span class="status valid">可用</span>
                <div class="debug-info">
                    <div><span class="color-box" style="background-color: #2563eb;"></span>背景色: #2563eb (蓝色)</div>
                    <div><span class="color-box" style="background-color: white;"></span>文字色: white (白色)</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>样式对比测试</h2>
        
        <h3>不同背景下的可见性测试</h3>
        
        <!-- 白色背景 -->
        <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 6px;">
            <strong>白色背景：</strong>
            <button class="button outline">取消</button>
            <button class="button disabled">保存 (禁用)</button>
            <button class="button enabled">保存 (启用)</button>
        </div>

        <!-- 浅灰背景 -->
        <div style="background: #f9fafb; padding: 15px; margin: 10px 0; border-radius: 6px;">
            <strong>浅灰背景：</strong>
            <button class="button outline">取消</button>
            <button class="button disabled">保存 (禁用)</button>
            <button class="button enabled">保存 (启用)</button>
        </div>

        <!-- 深色背景 -->
        <div style="background: #374151; padding: 15px; margin: 10px 0; border-radius: 6px; color: white;">
            <strong style="color: white;">深色背景：</strong>
            <button class="button outline">取消</button>
            <button class="button disabled">保存 (禁用)</button>
            <button class="button enabled">保存 (启用)</button>
        </div>
    </div>

    <div class="container">
        <h2>交互测试</h2>
        <p>点击按钮测试交互反馈：</p>
        
        <div class="button-test">
            <button class="button outline" onclick="alert('取消按钮被点击')">取消</button>
            <button class="button disabled" onclick="alert('禁用按钮不应该能点击')" disabled>保存 (禁用)</button>
            <button class="button enabled" onclick="alert('启用按钮被点击')">保存 (启用)</button>
        </div>
    </div>

    <div class="container">
        <h2>CSS样式代码</h2>
        <div class="debug-info">
            <h4>修复后的内联样式：</h4>
            <pre style="background: #f3f4f6; padding: 10px; border-radius: 4px; overflow-x: auto;">
style={{
  backgroundColor: isFormValid() ? '#2563eb' : '#9ca3af',
  color: isFormValid() ? 'white' : '#4b5563',
  border: 'none',
  cursor: isFormValid() ? 'pointer' : 'not-allowed',
  minWidth: '80px',
  height: '36px'
}}
className="px-4 py-2 rounded-md font-medium transition-all duration-200 hover:opacity-90"
            </pre>
        </div>
    </div>

    <div class="container">
        <h2>问题诊断</h2>
        <div class="debug-info">
            <h4>可能的原因：</h4>
            <ul>
                <li>✅ <strong>CSS类冲突：</strong>使用内联样式覆盖可能的冲突</li>
                <li>✅ <strong>颜色对比度：</strong>使用更明显的颜色差异</li>
                <li>✅ <strong>尺寸固定：</strong>设置最小宽度和高度确保可见</li>
                <li>✅ <strong>边框移除：</strong>避免边框样式干扰</li>
                <li>✅ <strong>调试标识：</strong>开发环境下显示状态标识</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟表单验证状态切换
        let isValid = false;
        
        function toggleValidation() {
            isValid = !isValid;
            const buttons = document.querySelectorAll('.button.test-dynamic');
            buttons.forEach(btn => {
                if (isValid) {
                    btn.className = 'button enabled test-dynamic';
                    btn.style.backgroundColor = '#2563eb';
                    btn.style.color = 'white';
                    btn.disabled = false;
                    btn.textContent = '保存 (启用)';
                } else {
                    btn.className = 'button disabled test-dynamic';
                    btn.style.backgroundColor = '#9ca3af';
                    btn.style.color = '#4b5563';
                    btn.disabled = true;
                    btn.textContent = '保存 (禁用)';
                }
            });
        }
        
        // 添加动态测试按钮
        const container = document.createElement('div');
        container.className = 'container';
        container.innerHTML = `
            <h2>动态状态测试</h2>
            <p>点击切换按钮测试状态变化：</p>
            <div class="button-test">
                <button class="button outline">取消</button>
                <button class="button disabled test-dynamic">保存 (禁用)</button>
                <button class="button outline" onclick="toggleValidation()">切换状态</button>
            </div>
        `;
        document.body.appendChild(container);
    </script>
</body>
</html>
