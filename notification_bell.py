#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务完成提醒铃声
发出6声好听的铃声来提醒用户任务已完成
"""

import time
import sys
import platform

def play_bell_sound():
    """发出铃声的函数"""
    system = platform.system()
    
    if system == "Darwin":  # macOS
        # 使用系统铃声
        import os
        os.system("afplay /System/Library/Sounds/Glass.aiff")
    elif system == "Windows":
        # Windows系统铃声
        import winsound
        winsound.Beep(800, 300)  # 频率800Hz，持续300ms
    else:  # Linux和其他系统
        # 使用终端铃声
        print("\a", end="", flush=True)

def play_notification_bells():
    """播放6声提醒铃声"""
    print("🔔 任务完成提醒 - 播放6声铃声...")
    
    for i in range(6):
        print(f"🔔 第 {i+1} 声铃声", end="", flush=True)
        play_bell_sound()
        
        # 铃声之间的间隔
        if i < 5:  # 最后一声后不需要等待
            time.sleep(0.5)  # 500ms间隔
        print(" ✓")
    
    print("🎉 任务完成提醒播放完毕！")

if __name__ == "__main__":
    play_notification_bells()
