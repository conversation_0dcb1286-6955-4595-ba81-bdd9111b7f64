#!/usr/bin/env python3
"""
测试提现记录功能
"""

import os
import sys
import django
from decimal import Decimal

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'writepro_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from agent_management.models import AgentLevel, UserAgent, CommissionRecord, AgentPaymentInfo, WithdrawRequest
from system_settings.models import SystemSettings

User = get_user_model()

def create_test_withdraw_records():
    """创建测试提现记录"""
    print("🧪 创建测试提现记录...")
    
    try:
        # 获取测试用户
        test_user = User.objects.get(email='<EMAIL>')
        print(f"   使用测试用户: {test_user.email}")
        
        # 获取提现设置
        withdraw_settings = SystemSettings.get_withdraw_settings()
        fee_rate = withdraw_settings['withdraw_fee_rate']
        
        # 创建多个不同状态的提现记录
        test_records = [
            {
                'withdraw_amount': Decimal('500.00'),
                'status': 'pending',
                'payment_method': 'bank'
            },
            {
                'withdraw_amount': Decimal('1000.00'),
                'status': 'approved',
                'payment_method': 'wechat'
            },
            {
                'withdraw_amount': Decimal('300.00'),
                'status': 'completed',
                'payment_method': 'alipay',
                'payment_reference': 'PAY202401250001'
            },
            {
                'withdraw_amount': Decimal('200.00'),
                'status': 'rejected',
                'payment_method': 'bank',
                'reject_reason': '银行卡信息不完整'
            }
        ]
        
        created_records = []
        for i, record_data in enumerate(test_records):
            # 检查是否已存在相同金额的记录
            existing = WithdrawRequest.objects.filter(
                agent_user=test_user,
                withdraw_amount=record_data['withdraw_amount']
            ).first()
            
            if existing:
                print(f"   记录已存在: ¥{record_data['withdraw_amount']} - {existing.get_status_display()}")
                created_records.append(existing)
                continue
            
            # 创建收款信息快照
            payment_info_snapshot = {}
            if record_data['payment_method'] == 'bank':
                payment_info_snapshot = {
                    'bank_name': '中国银行',
                    'bank_account': '6217000000000001234',
                    'account_holder': '测试用户',
                    'contact_phone': '***********'
                }
            elif record_data['payment_method'] == 'wechat':
                payment_info_snapshot = {
                    'wechat_qr_code': 'data:image/png;base64,test_wechat_qr',
                    'contact_phone': '***********'
                }
            elif record_data['payment_method'] == 'alipay':
                payment_info_snapshot = {
                    'alipay_qr_code': 'data:image/png;base64,test_alipay_qr',
                    'contact_phone': '***********'
                }
            
            # 创建提现记录
            withdraw_request = WithdrawRequest.objects.create(
                agent_user=test_user,
                withdraw_amount=record_data['withdraw_amount'],
                fee_rate=fee_rate,
                status=record_data['status'],
                payment_method=record_data['payment_method'],
                payment_info_snapshot=payment_info_snapshot,
                reject_reason=record_data.get('reject_reason', ''),
                payment_reference=record_data.get('payment_reference', '')
            )
            
            created_records.append(withdraw_request)
            print(f"   创建记录: ¥{withdraw_request.withdraw_amount} - {withdraw_request.get_status_display()}")
        
        print(f"\n✅ 测试记录创建完成，共 {len(created_records)} 条记录")
        
        # 显示记录详情
        print("\n📋 提现记录详情:")
        print("-" * 80)
        print(f"{'ID':<5} {'金额':<10} {'手续费':<8} {'实际到账':<10} {'状态':<8} {'收款方式':<8} {'创建时间':<20}")
        print("-" * 80)
        
        for record in created_records:
            print(f"{record.id:<5} ¥{record.withdraw_amount:<9} ¥{record.fee_amount:<7.2f} ¥{record.actual_amount:<9.2f} {record.get_status_display():<8} {record.payment_method:<8} {record.created_at.strftime('%Y-%m-%d %H:%M'):<20}")
        
        print("-" * 80)
        
        # 测试API响应格式
        print("\n🔍 测试API响应格式:")
        from agent_management.serializers import WithdrawRequestSerializer
        
        serializer = WithdrawRequestSerializer(created_records, many=True)
        print(f"   序列化成功，返回 {len(serializer.data)} 条记录")
        
        # 显示第一条记录的JSON格式
        if serializer.data:
            import json
            print(f"   示例记录JSON:")
            print(json.dumps(serializer.data[0], indent=2, ensure_ascii=False, default=str))
        
        return created_records
        
    except Exception as e:
        print(f"\n❌ 创建测试记录失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def test_withdraw_records_api():
    """测试提现记录API"""
    print("\n🌐 测试提现记录API...")
    
    try:
        import requests
        
        # 测试获取提现记录API
        url = 'http://localhost:8000/api/agent/admin/withdraw/records/'
        headers = {
            'Authorization': 'Bearer test_token',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(url, headers=headers)
        print(f"   API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                records = data.get('data', {}).get('results', [])
                print(f"   ✅ API调用成功，返回 {len(records)} 条记录")
                
                if records:
                    print(f"   示例记录: ID {records[0]['id']}, 金额 ¥{records[0]['withdraw_amount']}, 状态 {records[0]['status_display']}")
            else:
                print(f"   ❌ API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"   ❌ API调用失败: {response.text}")
            
    except Exception as e:
        print(f"   ⚠️ API测试跳过 (需要服务器运行): {str(e)}")

if __name__ == '__main__':
    print("🧪 提现记录功能测试")
    print("=" * 50)
    
    # 创建测试记录
    records = create_test_withdraw_records()
    
    # 测试API
    test_withdraw_records_api()
    
    print("\n🎉 测试完成！")
    print("\n💡 提示:")
    print("   1. 前端页面现在应该能显示这些测试记录")
    print("   2. 可以在代理后台查看提现记录功能")
    print("   3. 支持分页、刷新等操作")
    
    # 询问是否清理测试数据
    cleanup = input("\n是否清理测试数据？(y/N): ").lower().strip()
    if cleanup == 'y':
        print("\n🧹 清理测试数据...")
        WithdrawRequest.objects.filter(agent_user__email='<EMAIL>').delete()
        print("   测试数据已清理")
