#!/usr/bin/env python3
"""
测试支付时字数计算修复

验证支付处理时不会错误地重新计算.doc文档的字数
"""

import os
import sys
import django
from decimal import Decimal

# 设置Django环境
sys.path.append('/Users/<USER>/Downloads/aigc10')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'writepro_backend.settings')
django.setup()

from reduce_aigc.models import ReduceOrder
from reduce_aigc.views import ProcessPaymentView

def test_word_count_calculation():
    """测试字数计算逻辑"""
    print("🧪 测试支付时的字数计算修复")
    print("=" * 60)
    
    # 获取测试订单
    try:
        order = ReduceOrder.objects.get(id='ORD_20250718_002')
        
        print(f"测试订单信息:")
        print(f"  订单ID: {order.id}")
        print(f"  文件名: {order.file_name}")
        print(f"  当前字数: {order.word_count}")
        print(f"  原始内容: {order.original_content[:100]}...")
        
        # 创建支付视图实例来测试字数计算
        payment_view = ProcessPaymentView()
        
        # 测试原始的_count_words方法
        simple_count = payment_view._count_words(order.original_content)
        print(f"\n字数计算测试:")
        print(f"  简单字数统计: {simple_count}")
        
        # 测试修复后的逻辑
        if order.original_content.startswith('[Word文档:') and ('估算约' in order.original_content or '估算' in order.original_content):
            import re
            match = re.search(r'估算约?(\d+)', order.original_content)
            if match:
                estimated_count = int(match.group(1))
                print(f"  估算字数提取: {estimated_count}")
                print(f"  ✅ 应该使用估算字数: {estimated_count}")
            else:
                print(f"  ❌ 估算字数提取失败")
        else:
            print(f"  ℹ️ 不是.doc文档估算信息")
        
        print(f"\n修复验证:")
        if order.original_content.startswith('[Word文档:'):
            print(f"  ✅ 检测到.doc文档估算信息")
            print(f"  ✅ 支付时应该保持字数: {order.word_count}")
            print(f"  ✅ 而不是使用简单统计: {simple_count}")
        else:
            print(f"  ℹ️ 普通文本，使用简单统计")
            
    except ReduceOrder.DoesNotExist:
        print("❌ 测试订单不存在")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def simulate_payment_word_count_logic(order):
    """模拟支付时的字数计算逻辑"""
    print(f"\n🔄 模拟支付时的字数计算逻辑:")
    
    payment_view = ProcessPaymentView()
    
    if order.original_content:
        # 使用修复后的逻辑
        if order.original_content.startswith('[Word文档:') and ('估算约' in order.original_content or '估算' in order.original_content):
            import re
            match = re.search(r'估算约?(\d+)', order.original_content)
            if match:
                estimated_count = int(match.group(1))
                actual_word_count = estimated_count
                print(f"  使用.doc文档估算字数: {estimated_count}")
            else:
                actual_word_count = payment_view._count_words(order.original_content)
                print(f"  估算提取失败，使用简单统计: {actual_word_count}")
        else:
            actual_word_count = payment_view._count_words(order.original_content)
            print(f"  普通文本，使用简单统计: {actual_word_count}")
        
        if actual_word_count != order.word_count:
            print(f"  ⚠️ 字数不匹配: 存储={order.word_count}, 计算={actual_word_count}")
            print(f"  📝 将更新为: {actual_word_count}")
        else:
            print(f"  ✅ 字数匹配: {actual_word_count}")
        
        return actual_word_count
    
    return 0

def main():
    """主函数"""
    try:
        test_word_count_calculation()
        
        # 获取测试订单进行模拟
        order = ReduceOrder.objects.get(id='ORD_20250718_002')
        final_word_count = simulate_payment_word_count_logic(order)
        
        print(f"\n📊 测试结果总结:")
        print(f"  原始字数: {order.word_count}")
        print(f"  修复后字数: {final_word_count}")
        
        if final_word_count == order.word_count:
            print(f"  ✅ 修复成功！支付时字数保持不变")
        else:
            print(f"  ❌ 修复失败！支付时字数仍会改变")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    main()
