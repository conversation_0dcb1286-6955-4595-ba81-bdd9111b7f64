<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提现功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #005a8b;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 提现功能测试页面</h1>
    
    <div class="container">
        <h2>1. 系统配置测试</h2>
        <button class="button" onclick="testSystemSettings()">获取系统配置</button>
        <div id="systemSettingsResult"></div>
    </div>

    <div class="container">
        <h2>2. 收款信息管理</h2>
        <button class="button" onclick="testGetPaymentInfo()">获取收款信息</button>
        <button class="button" onclick="showPaymentInfoForm()">配置收款信息</button>
        
        <div id="paymentInfoForm" style="display: none;">
            <h3>配置收款信息</h3>
            <div class="form-group">
                <label>银行名称:</label>
                <input type="text" id="bankName" placeholder="如：中国银行">
            </div>
            <div class="form-group">
                <label>银行卡号:</label>
                <input type="text" id="bankAccount" placeholder="请输入银行卡号">
            </div>
            <div class="form-group">
                <label>开户人姓名:</label>
                <input type="text" id="accountHolder" placeholder="请输入开户人姓名">
            </div>
            <div class="form-group">
                <label>联系电话:</label>
                <input type="text" id="contactPhone" placeholder="请输入联系电话">
            </div>
            <button class="button" onclick="savePaymentInfo()">保存收款信息</button>
        </div>
        
        <div id="paymentInfoResult"></div>
    </div>

    <div class="container">
        <h2>3. 提现申请</h2>
        <button class="button" onclick="testGetWithdrawSettings()">获取提现设置</button>
        
        <div class="form-group">
            <label>提现金额:</label>
            <input type="number" id="withdrawAmount" placeholder="请输入提现金额" min="100">
        </div>
        <div class="form-group">
            <label>收款方式:</label>
            <select id="paymentMethod">
                <option value="bank">银行卡</option>
                <option value="wechat">微信</option>
                <option value="alipay">支付宝</option>
            </select>
        </div>
        <button class="button" onclick="createWithdrawRequest()">申请提现</button>
        
        <div id="withdrawResult"></div>
    </div>

    <div class="container">
        <h2>4. 提现记录</h2>
        <button class="button" onclick="getWithdrawRecords()">获取提现记录</button>
        <div id="withdrawRecordsResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        
        // 模拟token（实际应该从登录获取）
        const mockToken = 'test_token_123';
        
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${isError ? 'error' : 'success'}">${content}</div>`;
        }
        
        function showInfo(elementId, content) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="info">${content}</div>`;
        }

        async function testSystemSettings() {
            try {
                const response = await fetch(`${API_BASE}/settings/public/`);
                const data = await response.json();
                
                if (data.success) {
                    const withdrawSettings = data.data.withdraw_settings;
                    showResult('systemSettingsResult', `
                        <h4>✅ 系统配置获取成功</h4>
                        <p><strong>最低提现金额:</strong> ¥${withdrawSettings.min_withdraw_amount}</p>
                        <p><strong>提现手续费比例:</strong> ${withdrawSettings.withdraw_fee_rate}%</p>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    `);
                } else {
                    showResult('systemSettingsResult', `❌ 获取系统配置失败: ${data.error}`, true);
                }
            } catch (error) {
                showResult('systemSettingsResult', `❌ 请求失败: ${error.message}`, true);
            }
        }

        async function testGetPaymentInfo() {
            try {
                const response = await fetch(`${API_BASE}/agent/admin/payment-info/`, {
                    headers: {
                        'Authorization': `Bearer ${mockToken}`
                    }
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    if (data.data) {
                        showResult('paymentInfoResult', `
                            <h4>✅ 收款信息获取成功</h4>
                            <p><strong>银行:</strong> ${data.data.bank_name || '未配置'}</p>
                            <p><strong>开户人:</strong> ${data.data.account_holder || '未配置'}</p>
                            <p><strong>可用收款方式:</strong> ${data.data.available_payment_methods?.join('、') || '无'}</p>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        `);
                    } else {
                        showInfo('paymentInfoResult', '📝 尚未配置收款信息');
                    }
                } else {
                    showResult('paymentInfoResult', `❌ 获取收款信息失败: ${data.error || response.statusText}`, true);
                }
            } catch (error) {
                showResult('paymentInfoResult', `❌ 请求失败: ${error.message}`, true);
            }
        }

        function showPaymentInfoForm() {
            document.getElementById('paymentInfoForm').style.display = 'block';
        }

        async function savePaymentInfo() {
            const formData = {
                bank_name: document.getElementById('bankName').value,
                bank_account: document.getElementById('bankAccount').value,
                account_holder: document.getElementById('accountHolder').value,
                contact_phone: document.getElementById('contactPhone').value
            };

            try {
                const response = await fetch(`${API_BASE}/agent/admin/payment-info/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${mockToken}`
                    },
                    body: JSON.stringify(formData)
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('paymentInfoResult', `
                        <h4>✅ 收款信息保存成功</h4>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    `);
                    document.getElementById('paymentInfoForm').style.display = 'none';
                } else {
                    showResult('paymentInfoResult', `❌ 保存收款信息失败: ${data.error || response.statusText}`, true);
                }
            } catch (error) {
                showResult('paymentInfoResult', `❌ 请求失败: ${error.message}`, true);
            }
        }

        async function testGetWithdrawSettings() {
            try {
                const response = await fetch(`${API_BASE}/agent/admin/withdraw/settings/`, {
                    headers: {
                        'Authorization': `Bearer ${mockToken}`
                    }
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('withdrawResult', `
                        <h4>✅ 提现设置获取成功</h4>
                        <p><strong>最低提现金额:</strong> ¥${data.data.min_withdraw_amount}</p>
                        <p><strong>提现手续费比例:</strong> ${data.data.withdraw_fee_rate}%</p>
                        <p><strong>可用余额:</strong> ¥${data.data.available_balance.toFixed(2)}</p>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    `);
                } else {
                    showResult('withdrawResult', `❌ 获取提现设置失败: ${data.error || response.statusText}`, true);
                }
            } catch (error) {
                showResult('withdrawResult', `❌ 请求失败: ${error.message}`, true);
            }
        }

        async function createWithdrawRequest() {
            const withdrawAmount = document.getElementById('withdrawAmount').value;
            const paymentMethod = document.getElementById('paymentMethod').value;

            if (!withdrawAmount || parseFloat(withdrawAmount) < 100) {
                showResult('withdrawResult', '❌ 请输入有效的提现金额（最低100元）', true);
                return;
            }

            const requestData = {
                withdraw_amount: parseFloat(withdrawAmount),
                payment_method: paymentMethod
            };

            try {
                const response = await fetch(`${API_BASE}/agent/admin/withdraw/create/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${mockToken}`
                    },
                    body: JSON.stringify(requestData)
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('withdrawResult', `
                        <h4>✅ 提现申请创建成功</h4>
                        <p><strong>申请ID:</strong> ${data.data.id}</p>
                        <p><strong>提现金额:</strong> ¥${data.data.withdraw_amount}</p>
                        <p><strong>手续费:</strong> ¥${data.data.fee_amount} (${data.data.fee_rate}%)</p>
                        <p><strong>实际到账:</strong> ¥${data.data.actual_amount}</p>
                        <p><strong>状态:</strong> ${data.data.status_display}</p>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    `);
                } else {
                    showResult('withdrawResult', `❌ 创建提现申请失败: ${data.error || response.statusText}`, true);
                }
            } catch (error) {
                showResult('withdrawResult', `❌ 请求失败: ${error.message}`, true);
            }
        }

        async function getWithdrawRecords() {
            try {
                const response = await fetch(`${API_BASE}/agent/admin/withdraw/records/`, {
                    headers: {
                        'Authorization': `Bearer ${mockToken}`
                    }
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const records = data.data.results;
                    let recordsHtml = `<h4>✅ 提现记录获取成功 (共${data.data.count}条)</h4>`;
                    
                    if (records.length > 0) {
                        recordsHtml += '<table border="1" style="width:100%; border-collapse: collapse; margin-top: 10px;">';
                        recordsHtml += '<tr><th>ID</th><th>金额</th><th>手续费</th><th>实际到账</th><th>状态</th><th>申请时间</th></tr>';
                        
                        records.forEach(record => {
                            recordsHtml += `
                                <tr>
                                    <td>${record.id}</td>
                                    <td>¥${record.withdraw_amount}</td>
                                    <td>¥${record.fee_amount}</td>
                                    <td>¥${record.actual_amount}</td>
                                    <td>${record.status_display}</td>
                                    <td>${new Date(record.created_at).toLocaleString('zh-CN')}</td>
                                </tr>
                            `;
                        });
                        
                        recordsHtml += '</table>';
                    } else {
                        recordsHtml += '<p>暂无提现记录</p>';
                    }
                    
                    recordsHtml += `<pre>${JSON.stringify(data.data, null, 2)}</pre>`;
                    showResult('withdrawRecordsResult', recordsHtml);
                } else {
                    showResult('withdrawRecordsResult', `❌ 获取提现记录失败: ${data.error || response.statusText}`, true);
                }
            } catch (error) {
                showResult('withdrawRecordsResult', `❌ 请求失败: ${error.message}`, true);
            }
        }

        // 页面加载时自动测试系统配置和提现记录
        window.onload = function() {
            testSystemSettings();
            getWithdrawRecords();
        };
    </script>
</body>
</html>
