// 全局配置文件
// 控制是否使用mock数据，等后端开发好后修改为false

export const config = {
  // API配置
  API_BASE_URL: 'http://localhost:8000/api', // 本地开发环境

  // 是否使用mock数据
  USE_MOCK: false, // 使用真实API进行后端集成开发
  
  // 其他配置
  APP_NAME: 'WritePro',
  APP_DESCRIPTION: 'AI内容优化专家',
  
  // 新用户默认积分
  DEFAULT_USER_POINTS: 500,
  
  // Token配置
  TOKEN_STORAGE_KEY: {
    ACCESS: 'access_token',
    REFRESH: 'refresh_token'
  }
}

// 代理后台模块配置
export const AGENT_ADMIN_CONFIG = {
  USE_MOCK_DATA: false, // 使用真实API
  API_BASE_URL: 'http://localhost:8000/api/agent/admin',  // 本地开发环境
  PUBLIC_API_BASE_URL: 'http://localhost:8000/api/agent/public'  // 本地开发环境
}

export default config
