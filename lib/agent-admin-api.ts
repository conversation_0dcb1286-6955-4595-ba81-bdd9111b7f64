import { AGENT_ADMIN_CONFIG } from './config'

// TypeScript 类型定义
export interface AgentLevel {
  id: number
  name: string
  commission_rate: number
  fee: number
  service_content: string
  sort_order: number
  is_active: boolean
  description: string
  created_at: string
  updated_at: string
}

export interface UserAgent {
  id: number
  user_id: number
  agent_level_id: number
  agent_mode: 'commission' | 'prepaid'
  custom_commission_rate?: number
  total_credit: number
  remaining_credit: number
  domain?: string
  is_active: boolean
  created_at: string
  updated_at: string
  user: {
    id: number
    email: string
    points: number
  }
  agent_level: AgentLevel
}

export interface CommissionRecord {
  id: number
  agent_user_id: number
  consumer_user_id: number
  order_id: string
  order_amount: number | string
  commission_rate: number | string
  commission_amount: number | string
  status: 'pending' | 'settled' | 'cancelled'
  created_at: string
  settled_at?: string
}

export interface CommissionStats {
  total_commission: number
  this_month_commission: number
  total_orders: number
  active_users: number
  commission_records: Array<{
    date: string
    commission: number
    orders: number
  }>
}

export interface CreateAgentLevelData {
  name: string
  commission_rate: number
  fee: number
  service_content: string
  sort_order: number
  is_active: boolean
  description?: string
}

export interface CreateUserAgentData {
  user_email?: string  // 新增邮箱字段，用于创建时查找用户
  user_id?: number     // 保留ID字段，用于编辑时
  agent_level_id: number
  agent_mode: 'commission' | 'prepaid'
  custom_commission_rate?: number
  total_credit?: number
  remaining_credit?: number
  domain?: string
  is_active: boolean
}

export interface GetUserAgentsParams {
  search?: string
  agent_level_id?: number
  is_active?: boolean
  page?: number
  page_size?: number
}

export interface GetCommissionRecordsParams {
  agent_user_id?: number
  consumer_user_id?: number
  start_date?: string
  end_date?: string
  status?: string
  page?: number
  page_size?: number
}

export interface DomainCheckResult {
  available: boolean
  domain: string
}

export interface BindDomainData {
  user_agent_id: number
  domain: string
}

export interface RechargeRecord {
  id: number
  agent_user: {
    id: number
    email: string
    points: number
  }
  recharge_amount: number
  credit_amount: number
  commission_rate: number
  status: 'pending' | 'paid' | 'cancelled'
  payment_code?: string
  created_at: string
  paid_at?: string
}

export interface BillRecord {
  id: number
  agent_user: {
    id: number
    email: string
    points: number
  }
  consumer_user: {
    id: number
    email: string
    points: number
  }
  order_id: string
  order_amount: number
  headquarters_commission: number
  remaining_credit: number
  created_at: string
}

export interface RechargeRequest {
  recharge_amount: number
}

export interface AgentInfo {
  agent_user_id: number
  agent_level: AgentLevel
  custom_commission_rate?: number
}

// 提现相关类型定义
export interface AgentPaymentInfo {
  id: number
  bank_name: string
  bank_account: string
  account_holder: string
  wechat_qr_code: string
  alipay_qr_code: string
  contact_phone: string
  has_payment_method: boolean
  available_payment_methods: string[]
  created_at: string
  updated_at: string
}

export interface WithdrawRequest {
  id: number
  agent_user: {
    id: number
    email: string
    points: number
  }
  withdraw_amount: number
  fee_rate: number
  fee_amount: number
  actual_amount: number
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  status_display: string
  reviewed_by?: {
    id: number
    email: string
    points: number
  }
  reviewed_at?: string
  reject_reason: string
  completed_by?: {
    id: number
    email: string
    points: number
  }
  completed_at?: string
  payment_reference: string
  payment_method: 'bank' | 'wechat' | 'alipay'
  payment_info_snapshot: any
  created_at: string
}

export interface WithdrawSettings {
  min_withdraw_amount: number
  withdraw_fee_rate: number
  available_balance: number
}

export interface WithdrawCreateRequest {
  withdraw_amount: number
  payment_method: 'bank' | 'wechat' | 'alipay'
}

export interface AgentDashboardData {
  agent_info: {
    id: number
    user: {
      id: number
      email: string
      points: number
    }
    agent_level: AgentLevel
    agent_mode: 'commission' | 'prepaid'
    custom_commission_rate?: number
    total_credit: number
    remaining_credit: number
    domain?: string
    is_active: boolean
  }
  stats: {
    total_users: number | string
    active_users: number | string
    total_revenue: number | string
    total_commission: number | string
    available_balance: number | string
    pending_withdraw: number | string
    this_month_commission: number | string
  }
  recent_commissions: CommissionRecord[]
}

export interface PaginatedResponse<T> {
  results: T[]
  count: number
  next?: string
  previous?: string
}

// Mock数据
const mockAgentLevels: AgentLevel[] = [
  {
    id: 1,
    name: "金牌代理",
    commission_rate: 10.0,
    is_active: true,
    description: "金牌代理商等级，享受最高分成比例",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },
  {
    id: 2,
    name: "银牌代理",
    commission_rate: 8.0,
    is_active: true,
    description: "银牌代理商等级",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },
  {
    id: 3,
    name: "铜牌代理",
    commission_rate: 5.0,
    is_active: true,
    description: "铜牌代理商等级",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  }
]

const mockUserAgents: UserAgent[] = [
  {
    id: 1,
    user_id: 123,
    agent_level_id: 1,
    custom_commission_rate: 12.0,
    domain: "agent1.example.com",
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    user: {
      id: 123,
      email: "<EMAIL>",
      points: 1000
    },
    agent_level: mockAgentLevels[0]
  },
  {
    id: 2,
    user_id: 124,
    agent_level_id: 2,
    custom_commission_rate: null,
    domain: "agent2.example.com",
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    user: {
      id: 124,
      email: "<EMAIL>",
      points: 500
    },
    agent_level: mockAgentLevels[1]
  }
]

const mockCommissionRecords: CommissionRecord[] = [
  {
    id: 1,
    agent_user_id: 123,
    consumer_user_id: 456,
    order_id: "ORD123456",
    order_amount: 100.0,
    commission_rate: 12.0,
    commission_amount: 12.0,
    status: 'settled',
    created_at: "2024-01-25T10:00:00Z",
    settled_at: "2024-01-25T10:05:00Z"
  },
  {
    id: 2,
    agent_user_id: 123,
    consumer_user_id: 457,
    order_id: "ORD123457",
    order_amount: 200.0,
    commission_rate: 12.0,
    commission_amount: 24.0,
    status: 'settled',
    created_at: "2024-01-24T15:30:00Z",
    settled_at: "2024-01-24T15:35:00Z"
  }
]

// API函数实现
export const getAgentLevels = async (): Promise<AgentLevel[]> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      setTimeout(() => resolve(mockAgentLevels), 500)
    })
  }
  
  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/agent-levels/`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    }
  })
  
  if (!response.ok) {
    throw new Error('获取代理商等级失败')
  }
  
  const data = await response.json()
  // Django REST Framework 直接返回分页数据
  return data.results
}

export const createAgentLevel = async (data: CreateAgentLevelData): Promise<AgentLevel> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const newLevel: AgentLevel = {
        id: Date.now(),
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      mockAgentLevels.push(newLevel)
      setTimeout(() => resolve(newLevel), 500)
    })
  }
  
  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/agent-levels/`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  
  if (!response.ok) {
    throw new Error('创建代理商等级失败')
  }
  
  const result = await response.json()
  // Django REST Framework 直接返回创建的对象
  return result
}

export const updateAgentLevel = async (id: number, data: Partial<CreateAgentLevelData>): Promise<AgentLevel> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const index = mockAgentLevels.findIndex(level => level.id === id)
      if (index !== -1) {
        mockAgentLevels[index] = {
          ...mockAgentLevels[index],
          ...data,
          updated_at: new Date().toISOString()
        }
        setTimeout(() => resolve(mockAgentLevels[index]), 500)
      } else {
        throw new Error('代理商等级不存在')
      }
    })
  }
  
  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/agent-levels/${id}/`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  
  if (!response.ok) {
    throw new Error('更新代理商等级失败')
  }
  
  const result = await response.json()
  // Django REST Framework 直接返回更新的对象
  return result
}

export const deleteAgentLevel = async (id: number): Promise<void> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const index = mockAgentLevels.findIndex(level => level.id === id)
      if (index !== -1) {
        mockAgentLevels.splice(index, 1)
        setTimeout(() => resolve(), 500)
      } else {
        throw new Error('代理商等级不存在')
      }
    })
  }
  
  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/agent-levels/${id}/`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    }
  })
  
  if (!response.ok) {
    throw new Error('删除代理商等级失败')
  }
}

export const getUserAgents = async (params?: GetUserAgentsParams): Promise<PaginatedResponse<UserAgent>> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      let filteredAgents = [...mockUserAgents]

      if (params?.search) {
        filteredAgents = filteredAgents.filter(agent =>
          agent.user.email.includes(params.search!) ||
          agent.domain?.includes(params.search!)
        )
      }

      if (params?.agent_level_id) {
        filteredAgents = filteredAgents.filter(agent =>
          agent.agent_level_id === params.agent_level_id
        )
      }

      if (params?.is_active !== undefined) {
        filteredAgents = filteredAgents.filter(agent =>
          agent.is_active === params.is_active
        )
      }

      setTimeout(() => resolve({
        results: filteredAgents,
        count: filteredAgents.length,
        next: null,
        previous: null
      }), 500)
    })
  }

  const queryParams = new URLSearchParams()
  if (params?.search) queryParams.append('search', params.search)
  if (params?.agent_level_id) queryParams.append('agent_level_id', params.agent_level_id.toString())
  if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString())
  if (params?.page) queryParams.append('page', params.page.toString())
  if (params?.page_size) queryParams.append('page_size', params.page_size.toString())

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/user-agents/?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error('获取代理商用户失败')
  }

  const data = await response.json()
  // Django REST Framework 直接返回分页数据，不需要 data 字段
  return data
}

export const createUserAgent = async (data: CreateUserAgentData): Promise<UserAgent> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const agentLevel = mockAgentLevels.find(level => level.id === data.agent_level_id)
      if (!agentLevel) {
        throw new Error('代理商等级不存在')
      }

      const newUserAgent: UserAgent = {
        id: Date.now(),
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user: {
          id: data.user_id,
          email: `user${data.user_id}@example.com`,
          points: 0
        },
        agent_level: agentLevel
      }
      mockUserAgents.push(newUserAgent)
      setTimeout(() => resolve(newUserAgent), 500)
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/user-agents/`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })

  if (!response.ok) {
    throw new Error('创建代理商用户失败')
  }

  const result = await response.json()
  // Django REST Framework 直接返回创建的对象
  return result
}

export const updateUserAgent = async (id: number, data: Partial<CreateUserAgentData>): Promise<UserAgent> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const index = mockUserAgents.findIndex(agent => agent.id === id)
      if (index !== -1) {
        mockUserAgents[index] = {
          ...mockUserAgents[index],
          ...data,
          updated_at: new Date().toISOString()
        }
        setTimeout(() => resolve(mockUserAgents[index]), 500)
      } else {
        throw new Error('代理商用户不存在')
      }
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/user-agents/${id}/`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })

  if (!response.ok) {
    const errorData = await response.text()
    console.error('更新代理商用户失败:', response.status, errorData)
    throw new Error(`更新代理商用户失败: ${response.status} - ${errorData}`)
  }

  const result = await response.json()
  // Django REST Framework 直接返回更新的对象
  return result
}

export const deleteUserAgent = async (id: number): Promise<void> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const index = mockUserAgents.findIndex(agent => agent.id === id)
      if (index !== -1) {
        mockUserAgents.splice(index, 1)
        setTimeout(() => resolve(), 500)
      } else {
        throw new Error('代理商用户不存在')
      }
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/user-agents/${id}/`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    }
  })

  if (!response.ok) {
    throw new Error('删除代理商用户失败')
  }
}

export const checkDomainAvailability = async (domain: string): Promise<DomainCheckResult> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const isUsed = mockUserAgents.some(agent => agent.domain === domain)
      setTimeout(() => resolve({
        available: !isUsed,
        domain
      }), 300)
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/check-domain/?domain=${encodeURIComponent(domain)}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error('检查域名可用性失败')
  }

  const data = await response.json()
  // 检查域名API返回格式可能包含success和data字段
  return data.success ? data.data : data
}

export const getCommissionRecords = async (params?: GetCommissionRecordsParams): Promise<PaginatedResponse<CommissionRecord>> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      let filteredRecords = [...mockCommissionRecords]

      if (params?.agent_user_id) {
        filteredRecords = filteredRecords.filter(record =>
          record.agent_user_id === params.agent_user_id
        )
      }

      if (params?.consumer_user_id) {
        filteredRecords = filteredRecords.filter(record =>
          record.consumer_user_id === params.consumer_user_id
        )
      }

      if (params?.status) {
        filteredRecords = filteredRecords.filter(record =>
          record.status === params.status
        )
      }

      setTimeout(() => resolve({
        results: filteredRecords,
        count: filteredRecords.length,
        next: null,
        previous: null
      }), 500)
    })
  }

  const queryParams = new URLSearchParams()
  if (params?.agent_user_id) queryParams.append('agent_user_id', params.agent_user_id.toString())
  if (params?.consumer_user_id) queryParams.append('consumer_user_id', params.consumer_user_id.toString())
  if (params?.start_date) queryParams.append('start_date', params.start_date)
  if (params?.end_date) queryParams.append('end_date', params.end_date)
  if (params?.status) queryParams.append('status', params.status)
  if (params?.page) queryParams.append('page', params.page.toString())
  if (params?.page_size) queryParams.append('page_size', params.page_size.toString())

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/commission-records/?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error('获取分成记录失败')
  }

  const data = await response.json()
  // Django REST Framework 直接返回分页数据
  return data
}

export const getCommissionStats = async (agentUserId: number): Promise<CommissionStats> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const agentRecords = mockCommissionRecords.filter(record =>
        record.agent_user_id === agentUserId && record.status === 'settled'
      )

      const totalCommission = agentRecords.reduce((sum, record) => sum + record.commission_amount, 0)
      const thisMonthRecords = agentRecords.filter(record => {
        const recordDate = new Date(record.created_at)
        const now = new Date()
        return recordDate.getMonth() === now.getMonth() && recordDate.getFullYear() === now.getFullYear()
      })
      const thisMonthCommission = thisMonthRecords.reduce((sum, record) => sum + record.commission_amount, 0)

      setTimeout(() => resolve({
        total_commission: totalCommission,
        this_month_commission: thisMonthCommission,
        total_orders: agentRecords.length,
        active_users: 5, // Mock数据
        commission_records: [
          { date: '2024-01-25', commission: 12.0, orders: 1 },
          { date: '2024-01-24', commission: 24.0, orders: 1 }
        ]
      }), 500)
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/commission-stats/${agentUserId}/`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error('获取分成统计失败')
  }

  const data = await response.json()
  // 统计API返回格式可能包含success和data字段
  return data.success ? data.data : data
}

// 获取代理商仪表板数据
export const getAgentDashboard = async (): Promise<AgentDashboardData> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      setTimeout(() => resolve({
        agent_info: {
          id: 1,
          user: {
            id: 123,
            email: "<EMAIL>",
            points: 1000
          },
          agent_level: mockAgentLevels[0],
          custom_commission_rate: 12.0,
          domain: "example.com",
          is_active: true
        },
        stats: {
          total_users: 156,
          active_users: 89,
          total_revenue: 125680.5,
          total_commission: 25136.1,
          available_balance: 8950.3,
          pending_withdraw: 2500.0,
          this_month_commission: 4580.2
        },
        recent_commissions: mockCommissionRecords
      }), 500)
    })
  }

  // 添加时间戳防止缓存（暂时移除Cache-Control头避免CORS问题）
  const timestamp = new Date().getTime()
  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/dashboard/?t=${timestamp}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error('获取代理商仪表板数据失败')
  }

  const data = await response.json()
  return data.success ? data.data : data
}

// 获取代理商的分成记录（用于代理后台）
export const getMyCommissionRecords = async (params?: GetCommissionRecordsParams): Promise<PaginatedResponse<CommissionRecord>> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      setTimeout(() => resolve({
        results: mockCommissionRecords,
        count: mockCommissionRecords.length,
        next: null,
        previous: null
      }), 500)
    })
  }

  const queryParams = new URLSearchParams()
  if (params?.start_date) queryParams.append('start_date', params.start_date)
  if (params?.end_date) queryParams.append('end_date', params.end_date)
  if (params?.status) queryParams.append('status', params.status)
  if (params?.page) queryParams.append('page', params.page.toString())
  if (params?.page_size) queryParams.append('page_size', params.page_size.toString())

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/my-commissions/?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error('获取分成记录失败')
  }

  const data = await response.json()
  return data.success ? data.data : data
}

export const getAgentByDomain = async (domain: string): Promise<AgentInfo | null> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const agent = mockUserAgents.find(agent => agent.domain === domain && agent.is_active)
      if (agent) {
        setTimeout(() => resolve({
          agent_user_id: agent.user_id,
          agent_level: agent.agent_level,
          custom_commission_rate: agent.custom_commission_rate
        }), 300)
      } else {
        setTimeout(() => resolve(null), 300)
      }
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.PUBLIC_API_BASE_URL}/agent-by-domain/?domain=${encodeURIComponent(domain)}`)

  if (!response.ok) {
    if (response.status === 404) {
      return null
    }
    throw new Error('获取代理商信息失败')
  }

  const data = await response.json()
  // 公共API返回格式可能包含success和data字段
  return data.success ? data.data : data
}

// 预付费模式相关API

// 创建充值订单
export const createRechargeOrder = async (rechargeData: RechargeRequest): Promise<{
  recharge_id: number
  payment_code: string
  recharge_amount: number
  credit_amount: number
  commission_rate: number
}> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      setTimeout(() => resolve({
        recharge_id: Date.now(),
        payment_code: `RECHARGE_${Date.now()}`,
        recharge_amount: rechargeData.recharge_amount,
        credit_amount: rechargeData.recharge_amount / 0.3, // 假设30%分成比例
        commission_rate: 30
      }), 500)
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/recharge/create/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    },
    body: JSON.stringify(rechargeData)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || '创建充值订单失败')
  }

  const result = await response.json()
  return result.success ? result.data : result
}

// 确认充值支付
export const confirmRechargePayment = async (rechargeId: number): Promise<{
  total_credit: number
  remaining_credit: number
}> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      setTimeout(() => resolve({
        total_credit: 10000,
        remaining_credit: 8500
      }), 500)
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/recharge/${rechargeId}/confirm/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    }
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || '确认充值支付失败')
  }

  const result = await response.json()
  return result.success ? result.data : result
}

// 获取充值记录
export const getRechargeRecords = async (params?: {
  page?: number
  page_size?: number
}): Promise<PaginatedResponse<RechargeRecord>> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const mockRecords: RechargeRecord[] = [
        {
          id: 1,
          agent_user: { id: 123, email: "<EMAIL>", points: 1000 },
          recharge_amount: 3000,
          credit_amount: 10000,
          commission_rate: 30,
          status: 'paid',
          payment_code: 'RECHARGE_123',
          created_at: '2024-01-25T10:00:00Z',
          paid_at: '2024-01-25T10:05:00Z'
        }
      ]
      setTimeout(() => resolve({
        results: mockRecords,
        count: mockRecords.length,
        next: null,
        previous: null
      }), 500)
    })
  }

  const queryParams = new URLSearchParams()
  if (params?.page) queryParams.append('page', params.page.toString())
  if (params?.page_size) queryParams.append('page_size', params.page_size.toString())
  // 添加时间戳防止缓存
  queryParams.append('t', new Date().getTime().toString())

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/recharge/records/?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    }
  })

  if (!response.ok) {
    throw new Error('获取充值记录失败')
  }

  const result = await response.json()
  return result.success ? result.data : result
}

// 获取账单记录
export const getBillRecords = async (params?: {
  page?: number
  page_size?: number
}): Promise<PaginatedResponse<BillRecord>> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const mockRecords: BillRecord[] = [
        {
          id: 1,
          agent_user: { id: 123, email: "<EMAIL>", points: 1000 },
          consumer_user: { id: 456, email: "<EMAIL>", points: 500 },
          order_id: 'ORD123456',
          order_amount: 100,
          remaining_credit: 9900,
          created_at: '2024-01-25T10:00:00Z'
        }
      ]
      setTimeout(() => resolve({
        results: mockRecords,
        count: mockRecords.length,
        next: null,
        previous: null
      }), 500)
    })
  }

  const queryParams = new URLSearchParams()
  if (params?.page) queryParams.append('page', params.page.toString())
  if (params?.page_size) queryParams.append('page_size', params.page_size.toString())
  // 添加时间戳防止缓存
  queryParams.append('t', new Date().getTime().toString())

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/bill/records/?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    }
  })

  if (!response.ok) {
    throw new Error('获取账单记录失败')
  }

  const result = await response.json()
  return result.success ? result.data : result
}

// ==================== 分成模式提现相关API ====================

// 获取收款信息
export const getPaymentInfo = async (): Promise<AgentPaymentInfo | null> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      setTimeout(() => resolve({
        id: 1,
        bank_name: '中国银行',
        bank_account: '6217***********1234',
        account_holder: '张三',
        wechat_qr_code: '',
        alipay_qr_code: '',
        contact_phone: '138****5678',
        has_payment_method: true,
        available_payment_methods: ['bank'],
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }), 500)
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/payment-info/`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    }
  })

  if (!response.ok) {
    throw new Error('获取收款信息失败')
  }

  const result = await response.json()
  return result.success ? result.data : null
}

// 保存收款信息
export const savePaymentInfo = async (paymentInfo: Partial<AgentPaymentInfo>): Promise<AgentPaymentInfo> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      setTimeout(() => resolve({
        id: 1,
        ...paymentInfo,
        has_payment_method: true,
        available_payment_methods: ['bank'],
        created_at: '2024-01-01T00:00:00Z',
        updated_at: new Date().toISOString()
      } as AgentPaymentInfo), 500)
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/payment-info/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    },
    body: JSON.stringify(paymentInfo)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || '保存收款信息失败')
  }

  const result = await response.json()
  return result.success ? result.data : result
}

// 获取提现设置
export const getWithdrawSettings = async (): Promise<WithdrawSettings> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      setTimeout(() => resolve({
        min_withdraw_amount: 100,
        withdraw_fee_rate: 2.0,
        available_balance: 1500.50
      }), 500)
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/withdraw/settings/`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    }
  })

  if (!response.ok) {
    throw new Error('获取提现设置失败')
  }

  const result = await response.json()
  return result.success ? result.data : result
}

// 创建提现申请
export const createWithdrawRequest = async (withdrawData: WithdrawCreateRequest): Promise<WithdrawRequest> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      setTimeout(() => resolve({
        id: Date.now(),
        agent_user: { id: 123, email: "<EMAIL>", points: 1000 },
        withdraw_amount: withdrawData.withdraw_amount,
        fee_rate: 2.0,
        fee_amount: withdrawData.withdraw_amount * 0.02,
        actual_amount: withdrawData.withdraw_amount * 0.98,
        status: 'pending',
        status_display: '待审核',
        reject_reason: '',
        payment_reference: '',
        payment_method: withdrawData.payment_method,
        payment_info_snapshot: {},
        created_at: new Date().toISOString()
      }), 500)
    })
  }

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/withdraw/create/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    },
    body: JSON.stringify(withdrawData)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || '创建提现申请失败')
  }

  const result = await response.json()
  return result.success ? result.data : result
}

// 获取提现记录
export const getWithdrawRecords = async (params?: {
  page?: number
  page_size?: number
  status?: string
}): Promise<PaginatedResponse<WithdrawRequest>> => {
  if (AGENT_ADMIN_CONFIG.USE_MOCK_DATA) {
    return new Promise(resolve => {
      const mockRecords: WithdrawRequest[] = [
        {
          id: 1,
          agent_user: { id: 123, email: "<EMAIL>", points: 1000 },
          withdraw_amount: 1000,
          fee_rate: 2.0,
          fee_amount: 20,
          actual_amount: 980,
          status: 'completed',
          status_display: '已打款',
          reject_reason: '',
          payment_reference: 'PAY123456',
          payment_method: 'bank',
          payment_info_snapshot: {},
          created_at: '2024-01-25T10:00:00Z',
          completed_at: '2024-01-26T10:00:00Z'
        }
      ]
      setTimeout(() => resolve({
        results: mockRecords,
        count: mockRecords.length,
        next: null,
        previous: null
      }), 500)
    })
  }

  const queryParams = new URLSearchParams()
  if (params?.page) queryParams.append('page', params.page.toString())
  if (params?.page_size) queryParams.append('page_size', params.page_size.toString())
  if (params?.status) queryParams.append('status', params.status)

  const response = await fetch(`${AGENT_ADMIN_CONFIG.API_BASE_URL}/withdraw/records/?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    }
  })

  if (!response.ok) {
    throw new Error('获取提现记录失败')
  }

  const result = await response.json()
  return result.success ? result.data : result
}
