from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONParser
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.http import HttpResponse, Http404
from django.core.files.base import ContentFile
from django.utils import timezone
from django.db import transaction
from django.core.paginator import Paginator
import logging
from authentication.models import UserPointsHistory
from system_settings.models import SystemSettings
from .models import ReduceOrder
from .serializers import (
    CreateReduceOrderSerializer, ReduceOrderSerializer,
    ReduceOrderListSerializer, ProcessPaymentSerializer,
    DownloadResultSerializer
)
from .services import reduce_aigc_service
import logging
import os
import tempfile
import re

User = get_user_model()
logger = logging.getLogger(__name__)


class CreateReduceOrderView(generics.CreateAPIView):
    """
    创建降AIGC订单
    """
    serializer_class = CreateReduceOrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser, JSONParser]

    def create(self, request, *args, **kwargs):
        """创建订单"""
        # 检查维护模式
        if SystemSettings.is_maintenance_mode():
            settings = SystemSettings.get_settings()
            return Response({
                'success': False,
                'message': settings.maintenance_message
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'message': '参数验证失败',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                order = serializer.save()

                # 返回订单信息
                return Response({
                    'success': True,
                    'data': {
                        'order_id': order.id,
                        'task_id': order.task_id,
                        'word_count': order.word_count,
                        'original_price': float(order.original_price),
                        'final_price': float(order.final_price),
                        'points_used': order.points_used,
                        'estimated_time': order.estimated_time or 5
                    }
                }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            return Response({
                'success': False,
                'message': '创建订单失败，请稍后重试'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ProcessPaymentView(generics.GenericAPIView):
    """
    处理订单支付
    """
    serializer_class = ProcessPaymentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def _count_words(self, text):
        """
        正确的中文字数统计
        - 中文字符：每个字符算1个字
        - 英文单词：按空格分隔计算单词数
        - 数字：连续数字算1个字
        - 标点符号：不计入字数
        """
        if not text:
            return 0

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())

        # 统计中文字符（包括中文标点）
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
        chinese_count = len(chinese_chars)

        # 移除中文字符后统计英文单词
        text_without_chinese = re.sub(r'[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]', ' ', text)
        # 统计英文单词（连续的字母组成的单词）
        english_words = re.findall(r'[a-zA-Z]+', text_without_chinese)
        english_count = len(english_words)

        # 统计数字（连续数字算作一个单位）
        numbers = re.findall(r'\d+', text)
        number_count = len(numbers)

        total_words = chinese_count + english_count + number_count
        return total_words  # 如果没有内容就返回0

    def post(self, request, order_id):
        """处理支付"""
        order = get_object_or_404(ReduceOrder, id=order_id, user=request.user)

        if order.status != 'pending':
            return Response({
                'success': False,
                'message': '订单状态不允许支付'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'message': '支付参数验证失败',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        payment_method = serializer.validated_data['payment_method']
        use_points = serializer.validated_data.get('use_points', False)
        points_amount = serializer.validated_data.get('points_amount', 0)

        try:
            with transaction.atomic():
                # 检查积分系统是否开启
                from system_settings.models import SystemSettings
                points_system_enabled = SystemSettings.is_points_system_enabled()

                # 验证价格防止作弊 - 重新计算订单价格
                original_word_count = order.word_count
                original_points_used = order.points_used

                # 重新计算字数（防止前端篡改）
                if order.original_content:
                    # 如果是.doc文档的估算信息，使用估算字数而不是重新计算
                    if order.original_content.startswith('[Word文档:') and ('估算约' in order.original_content or '估算' in order.original_content):
                        import re
                        # 提取估算字数范围的最小值
                        match = re.search(r'估算约?(\d+)', order.original_content)
                        if match:
                            estimated_count = int(match.group(1))
                            actual_word_count = estimated_count
                            logger.info(f"订单 {order.id} 使用.doc文档估算字数: {estimated_count}")
                        else:
                            actual_word_count = self._count_words(order.original_content)
                    else:
                        actual_word_count = self._count_words(order.original_content)

                    if actual_word_count != order.word_count:
                        logger.warning(f"订单 {order.id} 字数不匹配: 存储={order.word_count}, 实际={actual_word_count}")
                        order.word_count = actual_word_count

                # 重新计算价格（防止前端篡改价格）
                order.calculate_price()

                # 如果使用积分，验证积分数量
                if points_system_enabled and use_points and points_amount > 0:
                    # 检查积分余额
                    if request.user.points < points_amount:
                        return Response({
                            'success': False,
                            'message': f'积分不足，当前余额: {request.user.points}'
                        }, status=status.HTTP_400_BAD_REQUEST)

                    # 验证积分使用量是否合理（不能超过订单金额对应的积分）
                    max_points = int(order.original_price * 100)  # 1元=100积分
                    if points_amount > max_points:
                        return Response({
                            'success': False,
                            'message': f'积分使用量超出限制，最多可使用 {max_points} 积分'
                        }, status=status.HTTP_400_BAD_REQUEST)

                    # 扣除积分
                    request.user.subtract_points(points_amount)

                    # 记录积分变动
                    UserPointsHistory.objects.create(
                        user=request.user,
                        transaction_type='consume',
                        amount=-points_amount,
                        balance_before=request.user.points + points_amount,
                        balance_after=request.user.points,
                        description=f'降AIGC订单支付: {order.id}'
                    )

                    # 更新订单积分使用并重新计算价格
                    order.points_used = points_amount
                    order.calculate_price()

                # 验证最终价格（防止恶意支付0元订单）
                if order.final_price < 0:
                    order.final_price = 0

                logger.info(f"订单 {order.id} 价格验证完成: 字数={order.word_count}, 原价={order.original_price}, 积分={order.points_used}, 最终价格={order.final_price}")

                # 标记订单为已支付
                order.status = 'paid'
                order.save()
                print(f"💳 [支付处理] 订单 {order.id} 支付完成，开始创建分成记录")

                # 处理代理商相关逻辑
                agent_credit_sufficient = self._handle_agent_logic(order)
                print(f"💰 [支付处理] 订单 {order.id} 代理商逻辑处理完成")

                # 提交到第三方服务（只有在额度充足时才提交）
                if agent_credit_sufficient:
                    self._submit_to_service(order)
                    print(f"🔄 [支付处理] 订单 {order.id} 第三方服务提交完成")
                else:
                    print(f"⚠️ [支付处理] 订单 {order.id} 代理商额度不足，暂停第三方服务提交")

                # 如果是扫码支付且有剩余金额，生成虚拟二维码
                qr_code_url = None
                if payment_method == 'qrcode' and order.final_price > 0:
                    # 生成虚拟二维码（用于模拟支付）
                    qr_code_url = f"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

                return Response({
                    'success': True,
                    'data': {
                        'order_id': order.id,
                        'payment_status': 'completed' if order.final_price == 0 else 'pending_payment',
                        'qr_code_url': qr_code_url,
                        'remaining_amount': float(order.final_price)
                    }
                })

        except Exception as e:
            logger.error(f"处理支付失败: {e}")
            return Response({
                'success': False,
                'message': '支付处理失败，请稍后重试'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _handle_agent_logic(self, order):
        """处理代理商相关逻辑（分成记录创建和额度扣除）"""
        try:
            # 导入放在方法内部避免循环导入
            from agent_management.services import create_commission_for_order
            from agent_management.views import deduct_agent_credit, check_agent_credit_sufficient

            # 只有在订单有实际金额时才处理代理商逻辑
            if order.final_price <= 0:
                return True

            # 检查用户是否有推荐代理商
            if not order.user.referred_by_agent:
                logger.info(f"订单 {order.id} 用户无推荐代理商，跳过代理商逻辑")
                return True

            agent_user = order.user.referred_by_agent

            # 检查代理商是否为预付费模式
            try:
                from agent_management.models import UserAgent
                user_agent = UserAgent.objects.get(user=agent_user, is_active=True)

                if user_agent.agent_mode == 'prepaid':
                    # 预付费模式：直接扣除额度，允许负数
                    success, message, new_remaining = deduct_agent_credit(
                        agent_user=agent_user,
                        consumer_user=order.user,
                        order_id=str(order.id),
                        order_amount=order.final_price
                    )

                    if success:
                        if new_remaining < 0:
                            logger.warning(f"订单 {order.id} 预付费代理商额度扣除成功但余额为负: 扣除¥{order.final_price}, 剩余¥{new_remaining}")
                        else:
                            logger.info(f"订单 {order.id} 预付费代理商额度扣除成功: 扣除¥{order.final_price}, 剩余¥{new_remaining}")
                        return True
                    else:
                        logger.error(f"订单 {order.id} 预付费代理商额度扣除失败: {message}")
                        return False
                else:
                    # 分成模式：创建分成记录
                    commission_record = create_commission_for_order(
                        consumer_user_id=order.user.id,
                        order_id=order.id,
                        order_amount=float(order.final_price)
                    )

                    if commission_record:
                        logger.info(f"为订单 {order.id} 创建分成记录: {commission_record.id}, 代理商: {commission_record.agent_user.email}, 金额: ¥{commission_record.commission_amount}")
                    else:
                        logger.info(f"订单 {order.id} 分成记录创建失败")

                    return True

            except UserAgent.DoesNotExist:
                logger.warning(f"订单 {order.id} 推荐代理商不存在或不活跃")
                return True

        except Exception as e:
            # 代理商逻辑处理失败不应该影响订单支付
            logger.error(f"处理代理商逻辑失败: {e}, 订单: {order.id}")
            return True

    def _submit_to_service(self, order):
        """提交订单到第三方服务"""
        print(f"🚀 [第三方提交] 开始提交订单 {order.id} 到第三方服务")
        print(f"📋 [第三方提交] 订单信息 - 文件路径: {order.file_path}, 内容长度: {len(order.original_content or '')}")

        try:
            result = None
            file_mode_failed = False

            # 优先尝试文件模式（如果有文件且文件存在）
            if order.file_path:
                import os
                file_path = order.file_path.path
                if os.path.exists(file_path):
                    print(f"📁 [第三方提交] 使用文件模式提交，文件: {file_path}")
                    try:
                        result = reduce_aigc_service.submit_file_task(
                            file_path=file_path,
                            rewrite_type=order.rewrite_type,
                            language=order.language
                        )
                        print(f"📥 [第三方提交] 文件模式响应: {result}")
                    except Exception as file_error:
                        print(f"⚠️ [第三方提交] 文件模式提交失败: {file_error}")
                        file_mode_failed = True
                        result = None
                else:
                    print(f"⚠️ [第三方提交] 文件不存在: {file_path}，将使用文本模式")
                    file_mode_failed = True

            # 如果文件模式失败或没有文件，尝试文本模式
            if not result or file_mode_failed:
                # 检查是否有有效的文本内容
                text_content = order.original_content or ""
                if text_content and not text_content.startswith("[文档文件:"):
                    print(f"📝 [第三方提交] 使用文本模式提交，内容: {text_content[:50]}...")
                    result = reduce_aigc_service.submit_text_task(
                        text=text_content,
                        rewrite_type=order.rewrite_type,
                        language=order.language
                    )
                    print(f"📥 [第三方提交] 文本模式响应: {result}")
                else:
                    # 如果没有有效内容，标记为失败
                    error_msg = "无有效内容可处理：文件不存在且无文本内容"
                    order.mark_as_failed(error_msg)
                    print(f"❌ [第三方提交] {error_msg}")
                    return

            # 处理提交结果
            if result and result.get('success'):
                order.task_id = result['task_id']
                order.mark_as_processing()
                print(f"✅ [第三方提交] 订单 {order.id} 已提交到第三方服务，任务ID: {order.task_id}")
            else:
                error_msg = result.get('error', '未知错误') if result else '提交失败'
                order.mark_as_failed(f"提交失败: {error_msg}")
                print(f"❌ [第三方提交] 订单 {order.id} 提交失败: {error_msg}")

        except Exception as e:
            order.mark_as_failed(f"提交异常: {str(e)}")
            print(f"💥 [第三方提交] 订单 {order.id} 提交异常: {e}")
            import traceback
            print(f"💥 [第三方提交] 异常堆栈: {traceback.format_exc()}")


class ReduceOrderDetailView(generics.RetrieveAPIView):
    """
    查询订单详情
    """
    serializer_class = ReduceOrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        order_id = self.kwargs['order_id']
        return get_object_or_404(ReduceOrder, id=order_id, user=self.request.user)

    def retrieve(self, request, *args, **kwargs):
        """获取订单详情"""
        order = self.get_object()

        logger.info(f"📋 [订单详情] 查询订单: {order.id}, 状态: {order.status}, 任务ID: {order.task_id}")

        # 如果订单正在处理中，尝试同步状态
        if order.status == 'processing' and order.task_id:
            logger.info(f"🔄 [订单详情] 开始同步订单状态: {order.id}")
            self._sync_order_status(order)
        else:
            logger.info(f"⏭️ [订单详情] 跳过同步 - 状态: {order.status}, 任务ID: {order.task_id}")

        serializer = self.get_serializer(order)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def _sync_order_status(self, order):
        """同步订单状态"""
        try:
            logger.info(f"🌐 [状态同步] 查询第三方服务状态，任务ID: {order.task_id}")
            result = reduce_aigc_service.get_task_status(order.task_id)
            logger.info(f"📥 [状态同步] 第三方服务响应: {result}")

            if result['success']:
                task_status = result['status']
                progress = result.get('progress', 0)
                logger.info(f"✅ [状态同步] 任务状态: {task_status}, 进度: {progress}%")

                if task_status == 'completed':
                    # 任务完成，下载处理结果
                    logger.info(f"📥 [状态同步] 任务完成，开始下载结果")
                    download_result = reduce_aigc_service.download_result(order.task_id)

                    if download_result['success']:
                        # 解码处理结果
                        processed_content = download_result['content'].decode('utf-8')
                        logger.info(f"✅ [状态同步] 结果下载成功，内容长度: {len(processed_content)}")

                        order.mark_as_completed(
                            processed_content=processed_content,
                            final_detection_rate=20  # 模拟降低后的检测率
                        )
                    else:
                        logger.error(f"❌ [状态同步] 结果下载失败: {download_result.get('error')}")
                        # 即使下载失败，也标记为完成，但没有内容
                        order.mark_as_completed(
                            processed_content="",
                            final_detection_rate=20
                        )
                elif task_status == 'failed':
                    # 任务失败，获取详细错误信息
                    error_message = result.get('error') or result.get('message', '处理失败')
                    # 如果是第三方服务的内部错误，提供更友好的错误信息
                    if 'list index out of range' in str(error_message):
                        error_message = '文档格式不支持或文档内容解析失败，请尝试使用其他格式的文档或纯文本内容'
                    order.mark_as_failed(error_message)
                    logger.error(f"订单 {order.id} 处理失败: {error_message}")
                elif task_status == 'processing':
                    # 更新进度
                    order.update_progress(progress)

        except Exception as e:
            logger.error(f"同步订单状态失败: {e}")


class ReduceOrderListView(generics.ListAPIView):
    """
    获取用户订单列表
    """
    serializer_class = ReduceOrderListSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = ReduceOrder.objects.filter(user=self.request.user)

        # 状态筛选
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 订单类型筛选（这里只有reduce类型）
        order_type = self.request.query_params.get('order_type')
        if order_type and order_type not in ['reduce', 'all']:
            queryset = queryset.none()

        return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):
        """获取订单列表"""
        queryset = self.get_queryset()

        # 分页
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))

        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # TODO: 暂时移除订单列表的实时同步功能，避免性能问题
        # 对处理中的订单进行实时状态检查（限制数量避免性能问题）
        # processing_orders = [order for order in page_obj.object_list if order.status == 'processing' and order.task_id]
        # if processing_orders:
        #     logger.info(f"🔄 [降AIGC订单列表] 发现 {len(processing_orders)} 个处理中的订单，进行实时状态检查")
        #     for order in processing_orders[:5]:  # 最多检查5个订单，避免性能问题
        #         try:
        #             self._sync_single_order_status(order)
        #         except Exception as e:
        #             logger.error(f"💥 [降AIGC订单列表] 同步订单 {order.id} 状态失败: {e}")

        serializer = self.get_serializer(page_obj.object_list, many=True)

        return Response({
            'success': True,
            'data': {
                'orders': serializer.data,
                'total': paginator.count,
                'page': page,
                'page_size': page_size
            }
        })

    def _sync_single_order_status(self, order):
        """同步单个订单状态（简化版，用于列表页面）"""
        try:
            logger.info(f"🌐 [订单列表状态同步] 查询第三方服务状态，任务ID: {order.task_id}")
            result = reduce_aigc_service.get_task_status(order.task_id)

            if result['success']:
                task_status = result['status']
                progress = result.get('progress', 0)

                if task_status == 'completed':
                    # 任务完成，下载处理结果
                    download_result = reduce_aigc_service.download_result(order.task_id)
                    if download_result['success']:
                        processed_content = download_result['content'].decode('utf-8')
                        order.mark_as_completed(
                            processed_content=processed_content,
                            final_detection_rate=20
                        )
                    else:
                        order.mark_as_completed(
                            processed_content="",
                            final_detection_rate=20
                        )
                elif task_status == 'failed':
                    error_message = result.get('error') or result.get('message', '处理失败')
                    if 'list index out of range' in str(error_message):
                        error_message = '文档格式不支持或文档内容解析失败，请尝试使用其他格式的文档或纯文本内容'
                    order.mark_as_failed(error_message)
                elif task_status == 'processing':
                    order.update_progress(progress)

        except Exception as e:
            logger.error(f"同步订单状态失败: {e}")

    def _sync_order_status(self, order):
        """同步订单状态（复用订单详情的同步逻辑）"""
        try:
            logger.info(f"🌐 [状态同步] 查询第三方服务状态，任务ID: {order.task_id}")
            result = reduce_aigc_service.get_task_status(order.task_id)
            logger.info(f"📥 [状态同步] 第三方服务响应: {result}")

            if result['success']:
                task_status = result['status']
                progress = result.get('progress', 0)
                logger.info(f"✅ [状态同步] 任务状态: {task_status}, 进度: {progress}%")

                if task_status == 'completed':
                    # 任务完成，下载处理结果
                    logger.info(f"📥 [状态同步] 任务完成，开始下载结果")
                    download_result = reduce_aigc_service.download_result(order.task_id)

                    if download_result['success']:
                        # 解码处理结果
                        processed_content = download_result['content'].decode('utf-8')
                        logger.info(f"✅ [状态同步] 结果下载成功，内容长度: {len(processed_content)}")

                        order.mark_as_completed(
                            processed_content=processed_content,
                            final_detection_rate=20  # 模拟降低后的检测率
                        )
                    else:
                        logger.error(f"❌ [状态同步] 结果下载失败: {download_result.get('error')}")
                        # 即使下载失败，也标记为完成，但没有内容
                        order.mark_as_completed(
                            processed_content="",
                            final_detection_rate=20
                        )
                elif task_status == 'failed':
                    # 任务失败，获取详细错误信息
                    error_message = result.get('error') or result.get('message', '处理失败')
                    # 如果是第三方服务的内部错误，提供更友好的错误信息
                    if 'list index out of range' in str(error_message):
                        error_message = '文档格式不支持或文档内容解析失败，请尝试使用其他格式的文档或纯文本内容'
                    order.mark_as_failed(error_message)
                    logger.error(f"订单 {order.id} 处理失败: {error_message}")
                elif task_status == 'processing':
                    # 更新进度
                    order.update_progress(progress)

        except Exception as e:
            logger.error(f"同步订单状态失败: {e}")


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def download_order_result(request, order_id):
    """
    下载订单结果
    """
    order = get_object_or_404(ReduceOrder, id=order_id, user=request.user)

    if not order.is_completed:
        return Response({
            'success': False,
            'message': '订单尚未完成'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        if order.task_id:
            # 从第三方服务下载
            result = reduce_aigc_service.download_result(order.task_id)

            if result['success']:
                response = HttpResponse(
                    result['content'],
                    content_type=result['content_type']
                )
                response['Content-Disposition'] = f'attachment; filename="{result["filename"]}"'
                return response
            else:
                # 如果第三方下载失败，返回处理后的内容
                if order.processed_content:
                    content = order.processed_content.encode('utf-8')
                    response = HttpResponse(content, content_type='text/plain; charset=utf-8')
                    response['Content-Disposition'] = f'attachment; filename="processed_{order.file_name}"'
                    return response
                else:
                    return Response({
                        'success': False,
                        'message': '下载失败，无可用内容'
                    }, status=status.HTTP_404_NOT_FOUND)
        else:
            # 直接返回处理后的内容
            if order.processed_content:
                content = order.processed_content.encode('utf-8')
                response = HttpResponse(content, content_type='text/plain; charset=utf-8')
                response['Content-Disposition'] = f'attachment; filename="processed_{order.file_name}"'
                return response
            else:
                return Response({
                    'success': False,
                    'message': '无可下载内容'
                }, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        logger.error(f"下载订单结果失败: {e}")
        return Response({
            'success': False,
            'message': '下载失败，请稍后重试'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])  # 仅用于测试
def simulate_qrcode_payment_success(request):
    """模拟扫码支付成功（仅用于测试）"""
    import logging
    from system_settings.models import SystemSettings

    logger = logging.getLogger(__name__)

    try:
        # 检查模拟支付开关
        if not SystemSettings.is_mock_payment_enabled():
            logger.warning(f"模拟支付被禁用，拒绝降AIGC订单模拟支付请求，IP: {request.META.get('REMOTE_ADDR')}")
            return Response({
                'success': False,
                'message': '模拟支付功能已被禁用'
            }, status=status.HTTP_403_FORBIDDEN)

        order_id = request.data.get('order_id')
        if not order_id:
            return Response({
                'success': False,
                'message': '缺少订单ID'
            }, status=status.HTTP_400_BAD_REQUEST)

        order = get_object_or_404(ReduceOrder, id=order_id)

        if order.status != 'paid':
            return Response({
                'success': False,
                'message': '订单状态不正确，只能对已支付但未完成的订单进行模拟支付'
            }, status=status.HTTP_400_BAD_REQUEST)

        if order.final_price <= 0:
            return Response({
                'success': False,
                'message': '该订单无需额外支付'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 模拟扫码支付成功，将剩余金额设为0
        order.final_price = 0
        order.save()

        # 记录模拟支付日志
        logger.info(f"🧪 [模拟支付] 降AIGC订单扫码支付成功 - 订单ID: {order_id}, 用户: {order.user.email}, IP: {request.META.get('REMOTE_ADDR')}")

        # 如果代理商额度充足，提交到第三方服务
        try:
            # 检查代理商额度是否充足
            if order.user.referred_by_agent:
                from agent_management.views import check_agent_credit_sufficient
                agent_sufficient = check_agent_credit_sufficient(
                    agent_user=order.user.referred_by_agent,
                    order_amount=order.original_price
                )
                if agent_sufficient:
                    # 提交到第三方服务
                    payment_view = ProcessPaymentView()
                    payment_view._submit_to_service(order)
                    logger.info(f"🔄 [模拟支付] 订单 {order_id} 已提交到第三方服务")
                else:
                    logger.warning(f"⚠️ [模拟支付] 订单 {order_id} 代理商额度不足，暂停第三方服务提交")
            else:
                # 无代理商，直接提交
                payment_view = ProcessPaymentView()
                payment_view._submit_to_service(order)
                logger.info(f"🔄 [模拟支付] 订单 {order_id} 已提交到第三方服务")
        except Exception as e:
            logger.error(f"提交第三方服务失败: {e}")

        return Response({
            'success': True,
            'message': f'订单 {order_id} 扫码支付模拟成功'
        })

    except Exception as e:
        logger.error(f"模拟扫码支付失败: {e}")
        return Response({
            'success': False,
            'message': f'模拟支付失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
